<?php
/**
 * API认证管理器
 */

class AuthManager
{
    private $db;
    private $secretKey;

    public function __construct($db = null)
    {
        $this->db = $db;
        $this->secretKey = 'your-secret-key-here'; // 应该从配置文件读取
        $this->initAuthTables();
    }

    /**
     * 初始化认证相关表
     */
    private function initAuthTables()
    {
        if (!$this->db) return;

        $sql = "
            CREATE TABLE IF NOT EXISTS api_keys (
                id INT AUTO_INCREMENT PRIMARY KEY,
                key_id VARCHAR(32) NOT NULL UNIQUE,
                key_secret VARCHAR(64) NOT NULL,
                user_name VARCHAR(100) NOT NULL,
                user_email VARCHAR(200),
                rate_limit INT DEFAULT 1000,
                daily_limit INT DEFAULT 10000,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                last_used_at TIMESTAMP NULL,
                INDEX idx_key_id (key_id),
                INDEX idx_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        try {
            $this->db->pdo->exec($sql);
        } catch (PDOException $e) {
            // 表可能已存在
        }
    }

    /**
     * 生成API密钥
     */
    public function generateApiKey($userName, $userEmail = '', $rateLimit = 1000, $dailyLimit = 10000)
    {
        $keyId = $this->generateRandomString(16);
        $keySecret = $this->generateRandomString(32);
        
        if ($this->db) {
            $sql = "INSERT INTO api_keys (key_id, key_secret, user_name, user_email, rate_limit, daily_limit) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $this->db->pdo->prepare($sql);
            $stmt->execute([$keyId, hash('sha256', $keySecret), $userName, $userEmail, $rateLimit, $dailyLimit]);
        }

        return [
            'key_id' => $keyId,
            'key_secret' => $keySecret,
            'rate_limit' => $rateLimit,
            'daily_limit' => $dailyLimit
        ];
    }

    /**
     * 验证API密钥
     */
    public function validateApiKey($keyId, $keySecret)
    {
        if (!$this->db) {
            // 如果没有数据库，使用简单的硬编码验证
            return $this->validateSimpleKey($keyId, $keySecret);
        }

        $sql = "SELECT * FROM api_keys WHERE key_id = ? AND is_active = TRUE";
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute([$keyId]);
        $apiKey = $stmt->fetch();

        if (!$apiKey) {
            return false;
        }

        // 检查密钥是否匹配
        if (!hash_equals($apiKey['key_secret'], hash('sha256', $keySecret))) {
            return false;
        }

        // 检查是否过期
        if ($apiKey['expires_at'] && strtotime($apiKey['expires_at']) < time()) {
            return false;
        }

        // 更新最后使用时间
        $this->updateLastUsed($keyId);

        return $apiKey;
    }

    /**
     * 简单密钥验证（无数据库）
     */
    private function validateSimpleKey($keyId, $keySecret)
    {
        // 硬编码的测试密钥
        $validKeys = [
            'test_key_123' => 'test_secret_456',
            'demo_key_789' => 'demo_secret_012'
        ];

        return isset($validKeys[$keyId]) && $validKeys[$keyId] === $keySecret;
    }

    /**
     * 检查速率限制
     */
    public function checkRateLimit($keyId, $timeWindow = 3600)
    {
        if (!$this->db) {
            return true; // 无数据库时不限制
        }

        $sql = "SELECT COUNT(*) as request_count FROM access_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? SECOND) 
                AND user_agent LIKE ?";
        
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute([$timeWindow, "%{$keyId}%"]);
        $result = $stmt->fetch();

        // 获取用户的速率限制
        $apiKey = $this->getApiKeyInfo($keyId);
        $rateLimit = $apiKey['rate_limit'] ?? 1000;

        return $result['request_count'] < $rateLimit;
    }

    /**
     * 检查每日限制
     */
    public function checkDailyLimit($keyId)
    {
        if (!$this->db) {
            return true;
        }

        $sql = "SELECT COUNT(*) as request_count FROM access_logs 
                WHERE DATE(created_at) = CURDATE() 
                AND user_agent LIKE ?";
        
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute(["%{$keyId}%"]);
        $result = $stmt->fetch();

        $apiKey = $this->getApiKeyInfo($keyId);
        $dailyLimit = $apiKey['daily_limit'] ?? 10000;

        return $result['request_count'] < $dailyLimit;
    }

    /**
     * 生成JWT Token
     */
    public function generateJWT($payload, $expiresIn = 3600)
    {
        $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
        $payload['exp'] = time() + $expiresIn;
        $payload = json_encode($payload);

        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, $this->secretKey, true);
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $base64Header . "." . $base64Payload . "." . $base64Signature;
    }

    /**
     * 验证JWT Token
     */
    public function validateJWT($token)
    {
        $parts = explode('.', $token);
        if (count($parts) !== 3) {
            return false;
        }

        [$header, $payload, $signature] = $parts;

        $validSignature = hash_hmac('sha256', $header . "." . $payload, $this->secretKey, true);
        $validSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($validSignature));

        if (!hash_equals($signature, $validSignature)) {
            return false;
        }

        $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $payload)), true);
        
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return false; // Token已过期
        }

        return $payload;
    }

    /**
     * 获取API密钥信息
     */
    private function getApiKeyInfo($keyId)
    {
        if (!$this->db) return [];

        $sql = "SELECT * FROM api_keys WHERE key_id = ?";
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute([$keyId]);
        return $stmt->fetch() ?: [];
    }

    /**
     * 更新最后使用时间
     */
    private function updateLastUsed($keyId)
    {
        if (!$this->db) return;

        $sql = "UPDATE api_keys SET last_used_at = NOW() WHERE key_id = ?";
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute([$keyId]);
    }

    /**
     * 生成随机字符串
     */
    private function generateRandomString($length = 16)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $charactersLength = strlen($characters);
        $randomString = '';
        
        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        
        return $randomString;
    }

    /**
     * 禁用API密钥
     */
    public function disableApiKey($keyId)
    {
        if (!$this->db) return false;

        $sql = "UPDATE api_keys SET is_active = FALSE WHERE key_id = ?";
        $stmt = $this->db->pdo->prepare($sql);
        return $stmt->execute([$keyId]);
    }

    /**
     * 获取用户的API使用统计
     */
    public function getUserStats($keyId, $days = 7)
    {
        if (!$this->db) return [];

        $sql = "SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as requests,
                    AVG(response_time) as avg_response_time
                FROM access_logs 
                WHERE user_agent LIKE ? 
                AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                GROUP BY DATE(created_at)
                ORDER BY date DESC";
        
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute(["%{$keyId}%", $days]);
        
        return $stmt->fetchAll();
    }
}
?>
