<?php
/**
 * 网易云音乐解析器
 */

class NeteaseMusicParser extends AbstractParser
{
    private $aesKey = 'e82ckenh8dichen8';

    /**
     * 解析网易云音乐
     */
    public function parse($url)
    {
        try {
            $this->info("开始解析网易云音乐URL: $url");
            
            // 提取音乐ID
            $musicId = $this->extractVideoId($url);
            if (!$musicId) {
                return $this->handleError('无法提取音乐ID');
            }

            $this->debug("提取到音乐ID: $musicId");

            // 获取音乐信息
            $musicInfo = $this->getMusicInfo($musicId);
            if (!$musicInfo) {
                return $this->handleError('获取音乐信息失败');
            }

            // 格式化数据
            $formattedData = $this->formatData($musicInfo);
            $formattedData['source_url'] = $url;

            $this->info("网易云音乐解析成功");
            return $formattedData;

        } catch (Exception $e) {
            return $this->handleError('解析过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * 提取音乐ID
     */
    protected function extractVideoId($url)
    {
        // 处理短链接
        if (strpos($url, '163cn.tv') !== false) {
            $headers = get_headers($url, false, stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]));
            
            foreach ($headers as $header) {
                if (strpos($header, 'Location:') !== false) {
                    $url = trim(str_replace('Location:', '', $header));
                    break;
                }
            }
        }

        // 提取ID
        if (preg_match('/id=(\d+)/', $url, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * 获取音乐信息
     */
    protected function getVideoInfo($musicId)
    {
        return $this->getMusicInfo($musicId);
    }

    /**
     * 获取音乐信息
     */
    private function getMusicInfo($musicId)
    {
        try {
            // 获取歌曲详情
            $songDetail = $this->getSongDetail($musicId);
            if (!$songDetail) {
                return null;
            }

            // 获取播放URL
            $playUrl = $this->getPlayUrl($musicId);
            
            // 获取歌词
            $lyrics = $this->getLyrics($musicId);

            $song = $songDetail['songs'][0] ?? null;
            if (!$song) {
                return null;
            }

            // 构造标准化数据
            return [
                'title' => $song['name'] ?? '',
                'desc' => $song['name'] ?? '',
                'author' => $this->getArtistNames($song['ar'] ?? []),
                'author_id' => '',
                'avatar' => '',
                'cover' => $song['al']['picUrl'] ?? '',
                'url' => $playUrl,
                'music' => [
                    'title' => $song['name'] ?? '',
                    'author' => $this->getArtistNames($song['ar'] ?? []),
                    'url' => $playUrl,
                    'cover' => $song['al']['picUrl'] ?? '',
                    'album' => $song['al']['name'] ?? '',
                    'lyrics' => $lyrics
                ],
                'album' => $song['al']['name'] ?? '',
                'lyrics' => $lyrics,
                'type' => 'music'
            ];

        } catch (Exception $e) {
            $this->debug("获取网易云音乐信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取歌曲详情
     */
    private function getSongDetail($musicId)
    {
        $url = "https://interface3.music.163.com/api/v3/song/detail";
        $data = [
            'c' => json_encode([["id" => intval($musicId), "v" => 0]])
        ];

        $response = $this->httpClient->post($url, $data, [
            'User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154'
        ]);

        if ($response) {
            return json_decode($response, true);
        }

        return null;
    }

    /**
     * 获取播放URL（简化版本）
     */
    private function getPlayUrl($musicId)
    {
        try {
            // 这里使用简化的方法，实际项目中可能需要更复杂的加密
            $url = "https://interface3.music.163.com/eapi/song/enhance/player/url/v1";
            
            $payload = [
                'ids' => [$musicId],
                'level' => 'standard',
                'encodeType' => 'mp3'
            ];

            // 简化的请求，实际需要AES加密
            $response = $this->httpClient->post($url, $payload, [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154'
            ]);

            if ($response) {
                $data = json_decode($response, true);
                if (isset($data['data'][0]['url'])) {
                    return str_replace('http://', 'https://', $data['data'][0]['url']);
                }
            }

            // 如果获取失败，返回提示信息
            return '由于版权限制，无法获取播放链接';

        } catch (Exception $e) {
            return '由于版权限制，无法获取播放链接';
        }
    }

    /**
     * 获取歌词
     */
    private function getLyrics($musicId)
    {
        try {
            $url = "https://interface3.music.163.com/api/song/lyric";
            $data = [
                'id' => $musicId,
                'cp' => 'false',
                'tv' => '0',
                'lv' => '0',
                'rv' => '0',
                'kv' => '0',
                'yv' => '0',
                'ytv' => '0',
                'yrv' => '0'
            ];

            $response = $this->httpClient->post($url, $data, [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Safari/537.36 Chrome/91.0.4472.164 NeteaseMusicDesktop/2.10.2.200154'
            ]);

            if ($response) {
                $data = json_decode($response, true);
                return $data['lrc']['lyric'] ?? '';
            }

            return '';

        } catch (Exception $e) {
            return '';
        }
    }

    /**
     * 获取艺术家名称
     */
    private function getArtistNames($artists)
    {
        if (empty($artists)) {
            return '';
        }

        $names = [];
        foreach ($artists as $artist) {
            if (isset($artist['name'])) {
                $names[] = $artist['name'];
            }
        }

        return implode('/', $names);
    }
}
?>
