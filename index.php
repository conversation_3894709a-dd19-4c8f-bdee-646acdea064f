<?php
/**
 * 短视频去水印统一解析接口 - 增强版
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON> (Enhanced by AI)
 * @CreateTime: 2025/7/23
 * @Description: 统一的短视频去水印解析接口，支持多平台、认证、批量处理
 */

// 设置响应头
header("Access-Control-Allow-Origin: *");
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入核心文件
require_once 'config/config.php';
require_once 'core/VideoParserFactory.php';
require_once 'core/PlatformDetector.php';
require_once 'core/ResponseFormatter.php';
require_once 'core/CacheManager.php';
require_once 'core/DatabaseManager.php';
require_once 'core/AuthManager.php';
require_once 'core/BatchProcessor.php';
require_once 'utils/Logger.php';
require_once 'utils/RateLimiter.php';

class UnifiedVideoAPI
{
    private $platformDetector;
    private $parserFactory;
    private $responseFormatter;
    private $cacheManager;
    private $logger;
    private $db;
    private $authManager;
    private $batchProcessor;
    private $rateLimiter;

    public function __construct()
    {
        $this->platformDetector = new PlatformDetector();
        $this->parserFactory = new VideoParserFactory();
        $this->responseFormatter = new ResponseFormatter();
        $this->cacheManager = new CacheManager();
        $this->logger = new Logger();
        $this->rateLimiter = new RateLimiter();

        // 初始化数据库（可选）
        try {
            if (defined('DB_ENABLE') && DB_ENABLE) {
                $this->db = new DatabaseManager();
                $this->authManager = new AuthManager($this->db);
                $this->batchProcessor = new BatchProcessor($this, $this->db);
            }
        } catch (Exception $e) {
            $this->logger->warn("数据库初始化失败: " . $e->getMessage());
        }
    }

    /**
     * 处理解析请求
     */
    public function handleRequest()
    {
        $startTime = microtime(true);
        $clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        try {
            // API认证检查
            if (defined('AUTH_ENABLE') && AUTH_ENABLE && $this->authManager) {
                $authResult = $this->checkAuthentication();
                if ($authResult !== true) {
                    return $authResult;
                }
            }

            // 速率限制检查
            if (!$this->rateLimiter->isAllowed($clientIP)) {
                return $this->responseFormatter->error(429, '请求过于频繁，请稍后再试');
            }

            // 路由处理
            $route = $this->getRoute();

            switch ($route) {
                case 'batch':
                    return $this->handleBatchRequest();
                case 'status':
                    return $this->handleStatusRequest();
                default:
                    return $this->handleSingleRequest();
            }

        } catch (Exception $e) {
            $this->logger->error("Error handling request: " . $e->getMessage());
            return $this->responseFormatter->error(500, '服务器内部错误: ' . $e->getMessage());
        } finally {
            // 记录访问日志
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            if ($this->db) {
                $this->db->logAccess($clientIP, $userAgent, $_SERVER['REQUEST_URI'] ?? '', '', 200, $responseTime);
            }
        }
    }

    /**
     * 处理单个URL解析请求
     */
    public function handleSingleRequest()
    {
        // 获取请求参数
        $url = $this->getRequestUrl();

        if (empty($url)) {
            return $this->responseFormatter->error(400, 'URL参数不能为空');
        }

        return $this->handleSingleUrl($url);
    }

    /**
     * 处理单个URL（内部方法）
     */
    public function handleSingleUrl($url)
    {
        $startTime = microtime(true);

        try {
            // 验证URL格式
            if (!$this->isValidUrl($url)) {
                return $this->responseFormatter->error(400, 'URL格式不正确');
            }

            // 检查缓存
            $cacheKey = md5($url);
            $cachedResult = $this->cacheManager->get($cacheKey);
            if ($cachedResult) {
                $this->logger->info("Cache hit for URL: $url");
                return json_decode($cachedResult, true);
            }

            // 检测平台
            $platform = $this->platformDetector->detect($url);
            if (!$platform) {
                return $this->responseFormatter->error(400, '不支持的平台或URL格式');
            }

            $this->logger->info("Detected platform: $platform for URL: $url");

            // 获取解析器
            $parser = $this->parserFactory->createParser($platform);
            if (!$parser) {
                return $this->responseFormatter->error(500, '解析器创建失败');
            }

            // 执行解析
            $result = $parser->parse($url);

            if (!$result) {
                return $this->responseFormatter->error(404, '解析失败，请检查链接是否有效');
            }

            // 格式化响应
            $response = $this->responseFormatter->success($result);

            // 缓存结果
            $this->cacheManager->set($cacheKey, $response, CACHE_EXPIRE_TIME);

            // 保存到数据库
            if ($this->db) {
                $this->db->saveParseRecord($url, $platform, $result);
                $responseTime = round((microtime(true) - $startTime) * 1000, 2);
                $this->db->recordApiStats($platform, true, $responseTime);
            }

            $this->logger->info("Successfully parsed URL: $url");

            return json_decode($response, true);

        } catch (Exception $e) {
            // 记录失败统计
            if ($this->db && isset($platform)) {
                $responseTime = round((microtime(true) - $startTime) * 1000, 2);
                $this->db->recordApiStats($platform, false, $responseTime);
            }

            $this->logger->error("Error parsing URL: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 检查API认证
     */
    private function checkAuthentication()
    {
        $apiKey = $_SERVER['HTTP_X_API_KEY'] ?? $_GET['api_key'] ?? '';
        $apiSecret = $_SERVER['HTTP_X_API_SECRET'] ?? $_GET['api_secret'] ?? '';

        if (empty($apiKey)) {
            return $this->responseFormatter->error(401, '缺少API密钥');
        }

        $keyInfo = $this->authManager->validateApiKey($apiKey, $apiSecret);
        if (!$keyInfo) {
            return $this->responseFormatter->error(401, 'API密钥无效');
        }

        // 检查速率限制
        if (!$this->authManager->checkRateLimit($apiKey)) {
            return $this->responseFormatter->error(429, '超出速率限制');
        }

        if (!$this->authManager->checkDailyLimit($apiKey)) {
            return $this->responseFormatter->error(429, '超出每日限制');
        }

        return true;
    }

    /**
     * 获取路由
     */
    private function getRoute()
    {
        $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $pathParts = explode('/', trim($path, '/'));

        // 简单路由：/batch, /status
        if (in_array('batch', $pathParts)) {
            return 'batch';
        }

        if (in_array('status', $pathParts)) {
            return 'status';
        }

        return 'single';
    }

    /**
     * 处理批量请求
     */
    private function handleBatchRequest()
    {
        if (!defined('BATCH_ENABLE') || !BATCH_ENABLE || !$this->batchProcessor) {
            return $this->responseFormatter->error(501, '批量处理功能未启用');
        }

        $method = $_SERVER['REQUEST_METHOD'];

        if ($method === 'POST') {
            // 创建批量任务
            $input = json_decode(file_get_contents('php://input'), true);
            $urls = $input['urls'] ?? [];

            if (empty($urls) || !is_array($urls)) {
                return $this->responseFormatter->error(400, '请提供URL数组');
            }

            try {
                $result = $this->batchProcessor->createBatchJob($urls);
                return $this->responseFormatter->success($result, '批量任务已创建');
            } catch (Exception $e) {
                return $this->responseFormatter->error(400, $e->getMessage());
            }
        } elseif ($method === 'GET') {
            // 查询批量任务状态
            $jobId = $_GET['job_id'] ?? '';
            if (empty($jobId)) {
                return $this->responseFormatter->error(400, '请提供job_id参数');
            }

            $status = $this->batchProcessor->getBatchJobStatus($jobId);
            return $this->responseFormatter->success($status);
        }

        return $this->responseFormatter->error(405, '不支持的请求方法');
    }

    /**
     * 处理状态请求
     */
    private function handleStatusRequest()
    {
        $stats = [
            'api_info' => [
                'name' => API_NAME,
                'version' => API_VERSION,
                'timestamp' => time()
            ],
            'supported_platforms' => $this->platformDetector->getSupportedPlatforms(),
            'features' => [
                'database' => defined('DB_ENABLE') && DB_ENABLE,
                'authentication' => defined('AUTH_ENABLE') && AUTH_ENABLE,
                'batch_processing' => defined('BATCH_ENABLE') && BATCH_ENABLE,
                'caching' => CACHE_ENABLE,
                'logging' => LOG_ENABLE
            ]
        ];

        if ($this->db) {
            $stats['statistics'] = $this->db->getStatistics();
            $stats['popular_content'] = $this->db->getPopularContent();
        }

        return $this->responseFormatter->success($stats);
    }

    /**
     * 获取请求URL
     */
    private function getRequestUrl()
    {
        // 支持GET和POST请求
        if ($_SERVER['REQUEST_METHOD'] === 'GET') {
            return $_GET['url'] ?? '';
        } else {
            $input = json_decode(file_get_contents('php://input'), true);
            return $input['url'] ?? $_POST['url'] ?? '';
        }
    }

    /**
     * 验证URL格式
     */
    private function isValidUrl($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
}

// 处理请求
$api = new UnifiedVideoAPI();
$response = $api->handleRequest();

// 输出响应
echo is_string($response) ? $response : json_encode($response, JSON_UNESCAPED_UNICODE);
?>
