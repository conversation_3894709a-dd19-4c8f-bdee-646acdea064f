<?php
/**
 * 数据库管理器 - 用于持久化缓存和统计数据
 */

class DatabaseManager
{
    private $pdo;
    private $config;

    public function __construct($config = null)
    {
        $this->config = $config ?: [
            'host' => 'localhost',
            'dbname' => 'video_parser',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4'
        ];
        
        $this->connect();
        $this->initTables();
    }

    /**
     * 建立数据库连接
     */
    private function connect()
    {
        try {
            $dsn = "mysql:host={$this->config['host']};dbname={$this->config['dbname']};charset={$this->config['charset']}";
            $this->pdo = new PDO($dsn, $this->config['username'], $this->config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]);
        } catch (PDOException $e) {
            throw new Exception("数据库连接失败: " . $e->getMessage());
        }
    }

    /**
     * 初始化数据表
     */
    private function initTables()
    {
        $tables = [
            // 解析记录表
            'parse_records' => "
                CREATE TABLE IF NOT EXISTS parse_records (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    url_hash VARCHAR(64) NOT NULL UNIQUE,
                    original_url TEXT NOT NULL,
                    platform VARCHAR(50) NOT NULL,
                    title VARCHAR(500),
                    author VARCHAR(200),
                    video_url TEXT,
                    cover_url TEXT,
                    parse_data JSON,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_platform (platform),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // API统计表
            'api_statistics' => "
                CREATE TABLE IF NOT EXISTS api_statistics (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    date DATE NOT NULL,
                    platform VARCHAR(50) NOT NULL,
                    total_requests INT DEFAULT 0,
                    success_requests INT DEFAULT 0,
                    failed_requests INT DEFAULT 0,
                    avg_response_time DECIMAL(8,3) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_date_platform (date, platform)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // 用户访问记录表
            'access_logs' => "
                CREATE TABLE IF NOT EXISTS access_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent TEXT,
                    url TEXT NOT NULL,
                    platform VARCHAR(50),
                    status_code INT,
                    response_time DECIMAL(8,3),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_ip (ip_address),
                    INDEX idx_created_at (created_at),
                    INDEX idx_platform (platform)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "
        ];

        foreach ($tables as $name => $sql) {
            try {
                $this->pdo->exec($sql);
            } catch (PDOException $e) {
                throw new Exception("创建表 {$name} 失败: " . $e->getMessage());
            }
        }
    }

    /**
     * 保存解析记录
     */
    public function saveParseRecord($url, $platform, $data)
    {
        $urlHash = md5($url);
        
        $sql = "INSERT INTO parse_records (url_hash, original_url, platform, title, author, video_url, cover_url, parse_data) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                title = VALUES(title), 
                author = VALUES(author), 
                video_url = VALUES(video_url), 
                cover_url = VALUES(cover_url), 
                parse_data = VALUES(parse_data),
                updated_at = CURRENT_TIMESTAMP";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            $urlHash,
            $url,
            $platform,
            $data['title'] ?? '',
            $data['author']['name'] ?? '',
            $data['video']['url'] ?? '',
            $data['video']['cover'] ?? '',
            json_encode($data, JSON_UNESCAPED_UNICODE)
        ]);
    }

    /**
     * 获取解析记录
     */
    public function getParseRecord($url)
    {
        $urlHash = md5($url);
        $sql = "SELECT * FROM parse_records WHERE url_hash = ?";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$urlHash]);
        
        $record = $stmt->fetch();
        if ($record) {
            $record['parse_data'] = json_decode($record['parse_data'], true);
        }
        
        return $record;
    }

    /**
     * 记录API统计
     */
    public function recordApiStats($platform, $success, $responseTime)
    {
        $date = date('Y-m-d');
        
        $sql = "INSERT INTO api_statistics (date, platform, total_requests, success_requests, failed_requests, avg_response_time) 
                VALUES (?, ?, 1, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                total_requests = total_requests + 1,
                success_requests = success_requests + ?,
                failed_requests = failed_requests + ?,
                avg_response_time = (avg_response_time * (total_requests - 1) + ?) / total_requests";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([
            $date,
            $platform,
            $success ? 1 : 0,
            $success ? 0 : 1,
            $responseTime,
            $success ? 1 : 0,
            $success ? 0 : 1,
            $responseTime
        ]);
    }

    /**
     * 记录访问日志
     */
    public function logAccess($ip, $userAgent, $url, $platform, $statusCode, $responseTime)
    {
        $sql = "INSERT INTO access_logs (ip_address, user_agent, url, platform, status_code, response_time) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute([$ip, $userAgent, $url, $platform, $statusCode, $responseTime]);
    }

    /**
     * 获取统计数据
     */
    public function getStatistics($days = 7)
    {
        $sql = "SELECT 
                    platform,
                    SUM(total_requests) as total_requests,
                    SUM(success_requests) as success_requests,
                    SUM(failed_requests) as failed_requests,
                    AVG(avg_response_time) as avg_response_time
                FROM api_statistics 
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                GROUP BY platform";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$days]);
        
        return $stmt->fetchAll();
    }

    /**
     * 获取热门内容
     */
    public function getPopularContent($limit = 10)
    {
        $sql = "SELECT 
                    platform,
                    title,
                    author,
                    COUNT(*) as access_count
                FROM access_logs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                AND platform IS NOT NULL
                GROUP BY platform, title, author
                ORDER BY access_count DESC
                LIMIT ?";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll();
    }

    /**
     * 清理过期数据
     */
    public function cleanupOldData($days = 30)
    {
        $tables = [
            'access_logs' => 'created_at',
            'api_statistics' => 'date'
        ];
        
        $cleaned = 0;
        foreach ($tables as $table => $dateColumn) {
            $sql = "DELETE FROM {$table} WHERE {$dateColumn} < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$days]);
            $cleaned += $stmt->rowCount();
        }
        
        return $cleaned;
    }
}
?>
