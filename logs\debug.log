[2025-07-23 19:23:18] [xiaohongshu] 处理后的URL: http://xhslink.com/m/15e8F81IBxv
[2025-07-23 19:23:18] [xiaohongshu] getContentInfo开始: http://xhslink.com/m/15e8F81IBxv
[2025-07-23 19:23:19] [xiaohongshu] URL处理完成 | Context: {"finalUrl":"https:\/\/www.xiaohongshu.com\/discovery\/item\/687dc7760000000010012497?app_platform=ios&app_version=8.87&share_from_user_hidden=true&xsec_source=app_share&type=video&xsec_token=CBXITgKLvmjH1el_P2FHQ_KnRZuwOUZLj6kNLYcpO2DOM=&author_share=1&xhsshare=CopyLink&shareRedId=ODxHMUU-PUI2NzUyOTgwNjdHOTo1Rz47&apptime=1753245300&&share_id=5c0485d581764d959f462cd290cd2b83","id":"687dc7760000000010012497"}
[2025-07-23 19:23:19] [xiaohongshu] HTTP请求成功 | Context: {"responseLength":114509}
[2025-07-23 19:23:19] [xiaohongshu] 找到__INITIAL_STATE__数据
[2025-07-23 19:23:19] [xiaohongshu] JSON解析成功，开始数据提取
[2025-07-23 19:23:19] [xiaohongshu] 视频URL检查: h265=找到, h264=未找到, final=有视频
[2025-07-23 19:23:19] [xiaohongshu] 找到视频URL: http://sns-video-bd.xhscdn.com/stream/79/110/114/01e87dc77482dd3a4f037001982b5a0d40_114.mp4
[2025-07-23 19:23:19] [xiaohongshu] 图片数据检查 | Context: {"imageData":"未找到"}
[2025-07-23 19:23:19] [xiaohongshu] 基础信息提取完成 | Context: {"author":"有","title":"有","cover":"有"}
[2025-07-23 19:23:19] [xiaohongshu] 检测到视频内容 | Context: {"videoUrl":"http:\/\/sns-video-bd.xhscdn.com\/stream\/79\/110\/114\/01e87dc77482dd3a4f037001982b5a0d40_114.mp4"}
[2025-07-23 19:23:19] [xiaohongshu] 数据构造完成 | Context: {"type":"video","hasTitle":true,"hasAuthor":true,"hasVideoUrl":true,"imageCount":0}
[2025-07-23 21:16:14] [xiaohongshu] 处理后的URL: http://xhslink.com/m/15e8F81IBxv
[2025-07-23 21:16:14] [xiaohongshu] getContentInfo开始: http://xhslink.com/m/15e8F81IBxv
[2025-07-23 21:16:15] [xiaohongshu] URL处理完成 | Context: {"finalUrl":"https:\/\/www.xiaohongshu.com\/discovery\/item\/687dc7760000000010012497?app_platform=ios&app_version=8.87&share_from_user_hidden=true&xsec_source=app_share&type=video&xsec_token=CBXITgKLvmjH1el_P2FHQ_KnRZuwOUZLj6kNLYcpO2DOM=&author_share=1&xhsshare=CopyLink&shareRedId=ODxHMUU-PUI2NzUyOTgwNjdHOTo1Rz47&apptime=1753245300&&share_id=5c0485d581764d959f462cd290cd2b83","id":"687dc7760000000010012497"}
[2025-07-23 21:16:16] [xiaohongshu] HTTP请求成功 | Context: {"responseLength":114626}
[2025-07-23 21:16:16] [xiaohongshu] 找到__INITIAL_STATE__数据
[2025-07-23 21:16:16] [xiaohongshu] JSON解析成功，开始数据提取
[2025-07-23 21:16:16] [xiaohongshu] 视频URL检查: h265=找到, h264=未找到, final=有视频
[2025-07-23 21:16:16] [xiaohongshu] 找到视频URL: http://sns-video-hs.xhscdn.com/stream/79/110/114/01e87dc77482dd3a4f037001982b5a0d40_114.mp4
[2025-07-23 21:16:16] [xiaohongshu] 图片数据检查 | Context: {"imageData":"未找到"}
[2025-07-23 21:16:16] [xiaohongshu] 基础信息提取完成 | Context: {"author":"有","title":"有","cover":"有"}
[2025-07-23 21:16:16] [xiaohongshu] 检测到视频内容 | Context: {"videoUrl":"http:\/\/sns-video-hs.xhscdn.com\/stream\/79\/110\/114\/01e87dc77482dd3a4f037001982b5a0d40_114.mp4"}
[2025-07-23 21:16:16] [xiaohongshu] 数据构造完成 | Context: {"type":"video","hasTitle":true,"hasAuthor":true,"hasVideoUrl":true,"imageCount":0}
[2025-07-23 21:17:17] [xiaohongshu] 处理后的URL: http://xhslink.com/m/1VQ8tmxdKa5
[2025-07-23 21:17:17] [xiaohongshu] getContentInfo开始: http://xhslink.com/m/1VQ8tmxdKa5
[2025-07-23 21:17:18] [xiaohongshu] URL处理完成 | Context: {"finalUrl":"https:\/\/www.xiaohongshu.com\/discovery\/item\/67b7f990000000000b017033?app_platform=ios&app_version=8.87&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CBAnmXljgbX6F0fumlWIGVH_fxFV7Vt6LdnHRtjHXyQaE=&author_share=1&xhsshare=CopyLink&shareRedId=ODxHMUU-PUI2NzUyOTgwNjdHOTo1Rz47&apptime=1753245332&&share_id=01195780fe3742aebfadaac6d8fd8be5","id":"67b7f990000000000b017033"}
[2025-07-23 21:17:19] [xiaohongshu] HTTP请求成功 | Context: {"responseLength":95355}
[2025-07-23 21:17:19] [xiaohongshu] 找到__INITIAL_STATE__数据
[2025-07-23 21:17:19] [xiaohongshu] JSON解析成功，开始数据提取
[2025-07-23 21:17:19] [xiaohongshu] 视频URL检查: h265=未找到, h264=未找到, final=无视频
[2025-07-23 21:17:19] [xiaohongshu] 未找到视频URL，将解析为图集
[2025-07-23 21:17:19] [xiaohongshu] 图片数据检查 | Context: {"imageData":"未找到"}
[2025-07-23 21:17:19] [xiaohongshu] 基础信息提取完成 | Context: {"author":"有","title":"有","cover":"有"}
[2025-07-23 21:17:19] [xiaohongshu] 检测到图集内容 | Context: {"imageCount":18}
[2025-07-23 21:17:19] [xiaohongshu] 数据构造完成 | Context: {"type":"image","hasTitle":true,"hasAuthor":true,"hasVideoUrl":false,"imageCount":18}
[2025-07-23 21:18:27] [kuaishou] 重定向后的URL: https://v.m.chenzhongtech.com/fw/photo/3x4432hqnhjbvqu?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=9&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x4432hqnhjbvqu&shareId=18489645411533&shareToken=X97CI7BcUwB523l&shareResourceType=PHOTO_OTHER&userId=3xramtbkhuziazc&shareType=1&et=1_a%2F2004817289164011794_scn0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18489645411533&appType=1&shareObjectId=5254856508806838571&shareUrlOpened=0&timestamp=1753245127635
[2025-07-23 21:18:27] [kuaishou] 提取到视频ID: 3x4432hqnhjbvqu, 类型: photo
[2025-07-23 21:18:27] [kuaishou] 识别为图集，使用图集解析方法
[2025-07-23 21:18:27] [kuaishou] 开始解析图集内容 | Context: {"url":"https:\/\/v.kuaishou.com\/ns3MMLr3","videoId":"3x4432hqnhjbvqu"}
[2025-07-23 21:18:27] [kuaishou] 获取重定向URL
[2025-07-23 21:18:29] [kuaishou] 重定向目标URL | Context: {"location":"https:\/\/v.m.chenzhongtech.com\/fw\/photo\/3x4432hqnhjbvqu?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=9&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x4432hqnhjbvqu&shareId=18489645411533&shareToken=X97CI7BcUwB523l&shareResourceType=PHOTO_OTHER&userId=3xramtbkhuziazc&shareType=1&et=1_a%2F2004817289164011794_scn0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18489645411533&appType=1&shareObjectId=5254856508806838571&shareUrlOpened=0&timestamp=1753245127635"}
[2025-07-23 21:18:29] [kuaishou] 开始请求页面内容
[2025-07-23 21:18:30] [kuaishou] HTTP请求成功 | Context: {"responseLength":167450}
[2025-07-23 21:18:30] [kuaishou] 开始提取INIT_STATE数据
[2025-07-23 21:18:30] [kuaishou] 成功找到INIT_STATE数据
[2025-07-23 21:18:30] [kuaishou] JSON字符串长度 | Context: {"length":36718}
[2025-07-23 21:18:30] [kuaishou] JSON解析失败，使用原项目的清理方法 | Context: {"error":"Syntax error"}
[2025-07-23 21:18:30] [kuaishou] 原项目清理方法也失败 | Context: {"error":"Syntax error"}
[2025-07-23 21:18:30] [kuaishou] 内容解析失败，返回错误
[2025-07-23 21:21:40] [kuaishou] 重定向后的URL: https://v.m.chenzhongtech.com/fw/photo/3x4432hqnhjbvqu?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=9&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x4432hqnhjbvqu&shareId=18489645411533&shareToken=X97CI7BcUwB523l&shareResourceType=PHOTO_OTHER&userId=3xramtbkhuziazc&shareType=1&et=1_a%2F2004817289164011794_scn0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18489645411533&appType=1&shareObjectId=5254856508806838571&shareUrlOpened=0&timestamp=1753245127635
[2025-07-23 21:21:40] [kuaishou] 提取到视频ID: 3x4432hqnhjbvqu, 类型: photo
[2025-07-23 21:21:40] [kuaishou] 识别为图集，使用图集解析方法
[2025-07-23 21:21:40] [kuaishou] 开始解析图集内容 | Context: {"url":"https:\/\/v.kuaishou.com\/ns3MMLr3","videoId":"3x4432hqnhjbvqu"}
[2025-07-23 21:21:40] [kuaishou] 获取重定向URL
[2025-07-23 21:21:41] [kuaishou] 重定向目标URL | Context: {"location":"https:\/\/v.m.chenzhongtech.com\/fw\/photo\/3x4432hqnhjbvqu?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=9&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x4432hqnhjbvqu&shareId=18489645411533&shareToken=X97CI7BcUwB523l&shareResourceType=PHOTO_OTHER&userId=3xramtbkhuziazc&shareType=1&et=1_a%2F2004817289164011794_scn0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18489645411533&appType=1&shareObjectId=5254856508806838571&shareUrlOpened=0&timestamp=1753245127635"}
[2025-07-23 21:21:41] [kuaishou] 开始请求页面内容
[2025-07-23 21:21:43] [kuaishou] HTTP请求成功 | Context: {"responseLength":167616}
[2025-07-23 21:21:43] [kuaishou] 开始提取INIT_STATE数据
[2025-07-23 21:21:43] [kuaishou] 成功找到INIT_STATE数据
[2025-07-23 21:21:43] [kuaishou] JSON字符串长度 | Context: {"length":36869}
[2025-07-23 21:21:43] [kuaishou] JSON解析失败，使用原项目的清理方法 | Context: {"error":"Syntax error"}
[2025-07-23 21:21:43] [kuaishou] 原项目清理方法也失败 | Context: {"error":"Syntax error"}
[2025-07-23 21:21:43] [kuaishou] 内容解析失败，返回错误
[2025-07-23 21:47:17] [kuaishou] 重定向后的URL: https://v.m.chenzhongtech.com/fw/photo/3x4432hqnhjbvqu?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=9&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x4432hqnhjbvqu&shareId=18489645411533&shareToken=X97CI7BcUwB523l&shareResourceType=PHOTO_OTHER&userId=3xramtbkhuziazc&shareType=1&et=1_a%2F2004817289164011794_scn0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18489645411533&appType=1&shareObjectId=5254856508806838571&shareUrlOpened=0&timestamp=1753245127635
[2025-07-23 21:47:17] [kuaishou] 提取到视频ID: 3x4432hqnhjbvqu, 类型: photo
[2025-07-23 21:47:17] [kuaishou] 识别为图集，使用图集解析方法
[2025-07-23 21:47:17] [kuaishou] 开始解析图集内容 | Context: {"url":"https:\/\/v.kuaishou.com\/ns3MMLr3","videoId":"3x4432hqnhjbvqu"}
[2025-07-23 21:47:17] [kuaishou] 获取重定向URL
[2025-07-23 21:47:18] [kuaishou] 重定向目标URL | Context: {"location":"https:\/\/v.m.chenzhongtech.com\/fw\/photo\/3x4432hqnhjbvqu?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=9&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x4432hqnhjbvqu&shareId=18489645411533&shareToken=X97CI7BcUwB523l&shareResourceType=PHOTO_OTHER&userId=3xramtbkhuziazc&shareType=1&et=1_a%2F2004817289164011794_scn0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18489645411533&appType=1&shareObjectId=5254856508806838571&shareUrlOpened=0&timestamp=1753245127635"}
[2025-07-23 21:47:18] [kuaishou] 开始请求页面内容
[2025-07-23 21:47:20] [kuaishou] HTTP请求成功 | Context: {"responseLength":167580}
[2025-07-23 21:47:20] [kuaishou] 开始提取INIT_STATE数据
[2025-07-23 21:47:20] [kuaishou] 成功找到INIT_STATE数据
[2025-07-23 21:47:20] [kuaishou] JSON字符串长度 | Context: {"length":36869}
[2025-07-23 21:47:20] [kuaishou] JSON解析失败，使用原项目的清理方法 | Context: {"error":"Syntax error"}
[2025-07-23 21:47:20] [kuaishou] 原项目清理方法也失败 | Context: {"error":"Syntax error"}
[2025-07-23 21:47:20] [kuaishou] 内容解析失败，返回错误
[2025-07-26 14:33:52] [douyin] 提取到视频ID: 7507760645661003047
[2025-07-26 14:37:55] [kuaishou] 重定向后的URL: https://v.m.chenzhongtech.com/fw/photo/3x4432hqnhjbvqu?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=9&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x4432hqnhjbvqu&shareId=18489645411533&shareToken=X97CI7BcUwB523l&shareResourceType=PHOTO_OTHER&userId=3xramtbkhuziazc&shareType=1&et=1_a%2F2004817289164011794_scn0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18489645411533&appType=1&shareObjectId=5254856508806838571&shareUrlOpened=0&timestamp=1753245127635
[2025-07-26 14:37:55] [kuaishou] 提取到视频ID: 3x4432hqnhjbvqu, 类型: photo
[2025-07-26 14:37:55] [kuaishou] 识别为图集，使用图集解析方法
[2025-07-26 14:37:55] [kuaishou] 开始解析图集内容 | Context: {"url":"https:\/\/v.kuaishou.com\/ns3MMLr3","videoId":"3x4432hqnhjbvqu"}
[2025-07-26 14:37:55] [kuaishou] 获取重定向URL
[2025-07-26 14:37:56] [kuaishou] 重定向目标URL | Context: {"location":"https:\/\/v.m.chenzhongtech.com\/fw\/photo\/3x4432hqnhjbvqu?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=9&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x4432hqnhjbvqu&shareId=18489645411533&shareToken=X97CI7BcUwB523l&shareResourceType=PHOTO_OTHER&userId=3xramtbkhuziazc&shareType=1&et=1_a%2F2004817289164011794_scn0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18489645411533&appType=1&shareObjectId=5254856508806838571&shareUrlOpened=0&timestamp=1753245127635"}
[2025-07-26 14:37:56] [kuaishou] 开始请求页面内容
[2025-07-26 14:37:58] [kuaishou] HTTP请求成功 | Context: {"responseLength":167712}
[2025-07-26 14:37:58] [kuaishou] 开始提取INIT_STATE数据
[2025-07-26 14:37:58] [kuaishou] 成功找到INIT_STATE数据
[2025-07-26 14:37:58] [kuaishou] JSON字符串长度 | Context: {"length":36724}
[2025-07-26 14:37:58] [kuaishou] JSON解析失败，使用原项目的清理方法 | Context: {"error":"Syntax error"}
[2025-07-26 14:37:58] [kuaishou] 原项目清理方法也失败 | Context: {"error":"Syntax error"}
[2025-07-26 14:37:58] [kuaishou] 内容解析失败，返回错误
[2025-07-26 14:38:21] [kuaishou] 重定向后的URL: https://v.m.chenzhongtech.com/fw/photo/3x88jfvqkz74fi2?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=10&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x88jfvqkz74fi2&shareId=18490021587214&shareToken=X-ZyNlHzk3909hL&shareResourceType=PHOTO_SELF&userId=3xfvbs7gw47vj72&shareType=2&et=1_i%2F2008561943978351297_p0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18490021587214&appType=1&shareObjectId=5217983283205771816&shareUrlOpened=0&timestamp=1753263375198
[2025-07-26 14:38:21] [kuaishou] 提取到视频ID: 3x88jfvqkz74fi2, 类型: photo
[2025-07-26 14:38:21] [kuaishou] 识别为图集，使用图集解析方法
[2025-07-26 14:38:21] [kuaishou] 开始解析图集内容 | Context: {"url":"https:\/\/v.kuaishou.com\/Ksx28Vyt","videoId":"3x88jfvqkz74fi2"}
[2025-07-26 14:38:21] [kuaishou] 获取重定向URL
[2025-07-26 14:38:23] [kuaishou] 重定向目标URL | Context: {"location":"https:\/\/v.m.chenzhongtech.com\/fw\/photo\/3x88jfvqkz74fi2?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=10&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x88jfvqkz74fi2&shareId=18490021587214&shareToken=X-ZyNlHzk3909hL&shareResourceType=PHOTO_SELF&userId=3xfvbs7gw47vj72&shareType=2&et=1_i%2F2008561943978351297_p0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18490021587214&appType=1&shareObjectId=5217983283205771816&shareUrlOpened=0&timestamp=1753263375198"}
[2025-07-26 14:38:23] [kuaishou] 开始请求页面内容
[2025-07-26 14:38:24] [kuaishou] HTTP请求成功 | Context: {"responseLength":164715}
[2025-07-26 14:38:24] [kuaishou] 开始提取INIT_STATE数据
[2025-07-26 14:38:24] [kuaishou] 成功找到INIT_STATE数据
[2025-07-26 14:38:24] [kuaishou] JSON字符串长度 | Context: {"length":33910}
[2025-07-26 14:38:24] [kuaishou] JSON解析失败，使用原项目的清理方法 | Context: {"error":"Syntax error"}
[2025-07-26 14:38:24] [kuaishou] 原项目清理方法也失败 | Context: {"error":"Syntax error"}
[2025-07-26 14:38:24] [kuaishou] 内容解析失败，返回错误
[2025-07-26 16:12:07] [douyin] 提取到视频ID: 7181349616976235828
[2025-07-26 16:17:43] [kuaishou] 重定向后的URL: https://v.m.chenzhongtech.com/fw/photo/3x88jfvqkz74fi2?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=10&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x88jfvqkz74fi2&shareId=18490021587214&shareToken=X-ZyNlHzk3909hL&shareResourceType=PHOTO_SELF&userId=3xfvbs7gw47vj72&shareType=2&et=1_i%2F2008561943978351297_p0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18490021587214&appType=1&shareObjectId=5217983283205771816&shareUrlOpened=0&timestamp=1753263375198
[2025-07-26 16:17:43] [kuaishou] 提取到视频ID: 3x88jfvqkz74fi2, 类型: photo
[2025-07-26 16:17:43] [kuaishou] 识别为图集，使用图集解析方法
[2025-07-26 16:17:43] [kuaishou] 开始解析图集内容 | Context: {"url":"https:\/\/v.kuaishou.com\/Ksx28Vyt","videoId":"3x88jfvqkz74fi2"}
[2025-07-26 16:17:43] [kuaishou] 获取重定向URL
[2025-07-26 16:17:45] [kuaishou] 重定向目标URL | Context: {"location":"https:\/\/v.m.chenzhongtech.com\/fw\/photo\/3x88jfvqkz74fi2?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=10&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x88jfvqkz74fi2&shareId=18490021587214&shareToken=X-ZyNlHzk3909hL&shareResourceType=PHOTO_SELF&userId=3xfvbs7gw47vj72&shareType=2&et=1_i%2F2008561943978351297_p0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18490021587214&appType=1&shareObjectId=5217983283205771816&shareUrlOpened=0&timestamp=1753263375198"}
[2025-07-26 16:17:45] [kuaishou] 开始请求页面内容
[2025-07-26 16:17:46] [kuaishou] HTTP请求成功 | Context: {"responseLength":164714}
[2025-07-26 16:17:46] [kuaishou] 开始提取INIT_STATE数据
[2025-07-26 16:17:46] [kuaishou] 成功找到INIT_STATE数据
[2025-07-26 16:17:46] [kuaishou] JSON字符串长度 | Context: {"length":33909}
[2025-07-26 16:17:46] [kuaishou] JSON解析失败，使用原项目的清理方法 | Context: {"error":"Syntax error"}
[2025-07-26 16:17:46] [kuaishou] 原项目清理方法也失败 | Context: {"error":"Syntax error"}
[2025-07-26 16:17:46] [kuaishou] 内容解析失败，返回错误
[2025-07-30 12:21:06] [kuaishou] 重定向后的URL: https://v.m.chenzhongtech.com/fw/photo/3x88jfvqkz74fi2?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=10&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x88jfvqkz74fi2&shareId=18490021587214&shareToken=X-ZyNlHzk3909hL&shareResourceType=PHOTO_SELF&userId=3xfvbs7gw47vj72&shareType=2&et=1_i%2F2008561943978351297_p0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18490021587214&appType=1&shareObjectId=5217983283205771816&shareUrlOpened=0&timestamp=1753263375198
[2025-07-30 12:21:06] [kuaishou] 提取到视频ID: 3x88jfvqkz74fi2, 类型: photo
[2025-07-30 12:21:06] [kuaishou] 识别为图集，使用图集解析方法
[2025-07-30 12:21:06] [kuaishou] 开始解析图集内容 | Context: {"url":"https:\/\/v.kuaishou.com\/Ksx28Vyt","videoId":"3x88jfvqkz74fi2"}
[2025-07-30 12:21:06] [kuaishou] 获取重定向URL
[2025-07-30 12:21:07] [kuaishou] 重定向目标URL | Context: {"location":"https:\/\/v.m.chenzhongtech.com\/fw\/photo\/3x88jfvqkz74fi2?cc=share_copylink&followRefer=151&shareMethod=TOKEN&docId=10&kpn=KUAISHOU&subBiz=BROWSE_SLIDE_PHOTO&photoId=3x88jfvqkz74fi2&shareId=18490021587214&shareToken=X-ZyNlHzk3909hL&shareResourceType=PHOTO_SELF&userId=3xfvbs7gw47vj72&shareType=2&et=1_i%2F2008561943978351297_p0&shareMode=APP&efid=3xfvbs7gw47vj72&originShareId=18490021587214&appType=1&shareObjectId=5217983283205771816&shareUrlOpened=0&timestamp=1753263375198"}
[2025-07-30 12:21:07] [kuaishou] 开始请求页面内容
[2025-07-30 12:21:09] [kuaishou] HTTP请求成功 | Context: {"responseLength":165327}
[2025-07-30 12:21:09] [kuaishou] 开始提取INIT_STATE数据
[2025-07-30 12:21:09] [kuaishou] 成功找到INIT_STATE数据
[2025-07-30 12:21:09] [kuaishou] JSON字符串长度 | Context: {"length":34522}
[2025-07-30 12:21:09] [kuaishou] JSON解析失败，使用原项目的清理方法 | Context: {"error":"Syntax error"}
[2025-07-30 12:21:09] [kuaishou] 原项目清理方法也失败 | Context: {"error":"Syntax error"}
[2025-07-30 12:21:09] [kuaishou] 内容解析失败，返回错误
