<?php
/**
 * 统一接口测试脚本
 */

require_once '../config/config.php';

class APITester
{
    private $baseUrl;
    private $testResults = [];

    public function __construct($baseUrl)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "🚀 开始运行API测试...\n\n";

        // 测试用例
        $testCases = [
            [
                'name' => '抖音视频测试',
                'url' => 'https://v.douyin.com/ieFvvmjh/',
                'platform' => 'douyin'
            ],
            [
                'name' => '快手视频测试',
                'url' => 'https://www.kuaishou.com/short-video/3xiqjrezhqjh4aq',
                'platform' => 'kuaishou'
            ],
            [
                'name' => '小红书测试',
                'url' => 'http://xhslink.com/a/R8U8OlsQw',
                'platform' => 'xiaoh<PERSON><PERSON>'
            ],
            [
                'name' => 'B站视频测试',
                'url' => 'https://www.bilibili.com/video/BV1xx411c7mu',
                'platform' => 'bilibili'
            ]
        ];

        foreach ($testCases as $testCase) {
            $this->runTest($testCase);
        }

        $this->printSummary();
    }

    /**
     * 运行单个测试
     */
    private function runTest($testCase)
    {
        echo "📋 测试: {$testCase['name']}\n";
        echo "🔗 URL: {$testCase['url']}\n";

        $startTime = microtime(true);
        
        try {
            $response = $this->makeRequest($testCase['url']);
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            if ($response) {
                $data = json_decode($response, true);
                
                if ($data && isset($data['code'])) {
                    if ($data['code'] === 200) {
                        echo "✅ 测试通过 ({$duration}ms)\n";
                        $this->validateResponse($data, $testCase);
                        $this->testResults[] = [
                            'name' => $testCase['name'],
                            'status' => 'PASS',
                            'duration' => $duration,
                            'platform' => $testCase['platform']
                        ];
                    } else {
                        echo "❌ 测试失败: {$data['msg']} (Code: {$data['code']})\n";
                        $this->testResults[] = [
                            'name' => $testCase['name'],
                            'status' => 'FAIL',
                            'duration' => $duration,
                            'error' => $data['msg']
                        ];
                    }
                } else {
                    echo "❌ 响应格式错误\n";
                    $this->testResults[] = [
                        'name' => $testCase['name'],
                        'status' => 'FAIL',
                        'duration' => $duration,
                        'error' => '响应格式错误'
                    ];
                }
            } else {
                echo "❌ 请求失败\n";
                $this->testResults[] = [
                    'name' => $testCase['name'],
                    'status' => 'FAIL',
                    'duration' => $duration,
                    'error' => '请求失败'
                ];
            }
        } catch (Exception $e) {
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);
            echo "❌ 异常: " . $e->getMessage() . "\n";
            $this->testResults[] = [
                'name' => $testCase['name'],
                'status' => 'ERROR',
                'duration' => $duration,
                'error' => $e->getMessage()
            ];
        }

        echo "\n";
    }

    /**
     * 发送HTTP请求
     */
    private function makeRequest($url)
    {
        $apiUrl = $this->baseUrl . '/index.php?url=' . urlencode($url);
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'API-Tester/1.0'
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL error: $error");
        }

        if ($httpCode !== 200) {
            throw new Exception("HTTP error: $httpCode");
        }

        return $response;
    }

    /**
     * 验证响应数据
     */
    private function validateResponse($data, $testCase)
    {
        $requiredFields = ['platform', 'type', 'title', 'author', 'video'];
        $missingFields = [];

        if (!isset($data['data'])) {
            echo "⚠️  缺少data字段\n";
            return;
        }

        $responseData = $data['data'];

        foreach ($requiredFields as $field) {
            if (!isset($responseData[$field])) {
                $missingFields[] = $field;
            }
        }

        if (!empty($missingFields)) {
            echo "⚠️  缺少字段: " . implode(', ', $missingFields) . "\n";
        }

        // 验证平台匹配
        if (isset($responseData['platform']) && $responseData['platform'] !== $testCase['platform']) {
            echo "⚠️  平台识别错误: 期望 {$testCase['platform']}, 实际 {$responseData['platform']}\n";
        }

        // 验证内容类型
        if (isset($responseData['type']) && !in_array($responseData['type'], ['video', 'image'])) {
            echo "⚠️  内容类型无效: {$responseData['type']}\n";
        }

        // 验证URL
        if (isset($responseData['video']['url']) && !empty($responseData['video']['url']) && 
            !filter_var($responseData['video']['url'], FILTER_VALIDATE_URL) && 
            strpos($responseData['video']['url'], '当前为图文解析') === false) {
            echo "⚠️  视频URL格式无效\n";
        }
    }

    /**
     * 打印测试摘要
     */
    private function printSummary()
    {
        echo "📊 测试摘要\n";
        echo str_repeat("=", 50) . "\n";

        $total = count($this->testResults);
        $passed = count(array_filter($this->testResults, function($r) { return $r['status'] === 'PASS'; }));
        $failed = count(array_filter($this->testResults, function($r) { return $r['status'] === 'FAIL'; }));
        $errors = count(array_filter($this->testResults, function($r) { return $r['status'] === 'ERROR'; }));

        echo "总测试数: $total\n";
        echo "通过: $passed\n";
        echo "失败: $failed\n";
        echo "错误: $errors\n";
        echo "成功率: " . round(($passed / $total) * 100, 2) . "%\n\n";

        // 详细结果
        foreach ($this->testResults as $result) {
            $status = $result['status'] === 'PASS' ? '✅' : '❌';
            echo "$status {$result['name']} ({$result['duration']}ms)\n";
            if (isset($result['error'])) {
                echo "   错误: {$result['error']}\n";
            }
        }

        echo "\n";
    }

    /**
     * 测试系统状态
     */
    public function testSystemStatus()
    {
        echo "🔍 测试系统状态...\n";
        
        try {
            $statusUrl = $this->baseUrl . '/admin/status.php';
            $response = $this->makeRequest($statusUrl);
            $data = json_decode($response, true);
            
            if ($data && isset($data['api_info'])) {
                echo "✅ 系统状态正常\n";
                echo "API版本: {$data['api_info']['version']}\n";
                echo "PHP版本: {$data['system_info']['php_version']}\n";
                
                if (isset($data['cache_stats']['enabled']) && $data['cache_stats']['enabled']) {
                    echo "缓存状态: 已启用\n";
                } else {
                    echo "缓存状态: 已禁用\n";
                }
            } else {
                echo "❌ 系统状态异常\n";
            }
        } catch (Exception $e) {
            echo "❌ 无法获取系统状态: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    // 命令行模式
    $baseUrl = $argv[1] ?? 'http://localhost/unified_api';
    echo "测试目标: $baseUrl\n\n";
    
    $tester = new APITester($baseUrl);
    $tester->testSystemStatus();
    $tester->runAllTests();
} else {
    // Web模式
    header('Content-Type: text/plain; charset=utf-8');
    $baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']);
    
    $tester = new APITester($baseUrl);
    $tester->testSystemStatus();
    $tester->runAllTests();
}
?>
