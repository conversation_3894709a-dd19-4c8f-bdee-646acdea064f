<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>短视频去水印统一解析接口 - 演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .input-group input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .result.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .result.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .video-info {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 20px;
            margin-top: 15px;
        }
        
        .video-cover {
            width: 100%;
            border-radius: 8px;
        }
        
        .video-details h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .video-details p {
            margin-bottom: 8px;
            color: #666;
        }
        
        .video-link {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 15px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .platforms {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .platform {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .platform:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .platform-icon {
            font-size: 2em;
            margin-bottom: 8px;
        }
        
        .examples {
            margin-top: 20px;
        }
        
        .example-link {
            display: inline-block;
            margin: 5px;
            padding: 5px 10px;
            background: #e9ecef;
            border-radius: 5px;
            text-decoration: none;
            color: #495057;
            font-size: 12px;
            cursor: pointer;
        }
        
        .example-link:hover {
            background: #667eea;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 短视频去水印</h1>
            <p>统一解析接口演示</p>
        </div>
        
        <div class="content">
            <div class="platforms">
                <div class="platform">
                    <div class="platform-icon">📱</div>
                    <div>抖音</div>
                </div>
                <div class="platform">
                    <div class="platform-icon">⚡</div>
                    <div>快手</div>
                </div>
                <div class="platform">
                    <div class="platform-icon">📖</div>
                    <div>小红书</div>
                </div>
                <div class="platform">
                    <div class="platform-icon">📺</div>
                    <div>哔哩哔哩</div>
                </div>
            </div>
            
            <form id="parseForm">
                <div class="input-group">
                    <label for="videoUrl">请输入视频链接：</label>
                    <input type="url" id="videoUrl" name="url" placeholder="https://v.douyin.com/xxxxxx" required>
                </div>
                
                <button type="submit" class="btn" id="parseBtn">🚀 开始解析</button>
            </form>
            
            <div class="examples">
                <strong>示例链接：</strong><br>
                <span class="example-link" onclick="setUrl('https://v.douyin.com/ieFvvmjh/')">抖音示例</span>
                <span class="example-link" onclick="setUrl('https://www.kuaishou.com/short-video/3xiqjrezhqjh4aq')">快手示例</span>
                <span class="example-link" onclick="setUrl('http://xhslink.com/a/R8U8OlsQw')">小红书示例</span>
                <span class="example-link" onclick="setUrl('https://www.bilibili.com/video/BV1xx411c7mu')">B站示例</span>
            </div>
            
            <div id="result"></div>
        </div>
    </div>

    <script>
        const form = document.getElementById('parseForm');
        const resultDiv = document.getElementById('result');
        const parseBtn = document.getElementById('parseBtn');
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const url = document.getElementById('videoUrl').value;
            if (!url) return;
            
            parseBtn.disabled = true;
            parseBtn.textContent = '解析中...';
            
            showLoading();
            
            try {
                const response = await fetch(`index.php?url=${encodeURIComponent(url)}`);
                const data = await response.json();
                
                if (data.code === 200) {
                    showSuccess(data.data);
                } else {
                    showError(data.msg || '解析失败');
                }
            } catch (error) {
                showError('网络错误：' + error.message);
            } finally {
                parseBtn.disabled = false;
                parseBtn.textContent = '🚀 开始解析';
            }
        });
        
        function showLoading() {
            resultDiv.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    <p>正在解析中，请稍候...</p>
                </div>
            `;
        }
        
        function showSuccess(data) {
            const videoUrl = data.video.url;
            const isImage = data.type === 'image' || data.images.length > 0;
            
            let content = `
                <div class="result success">
                    <h3>✅ 解析成功</h3>
                    <div class="video-info">
                        <div>
                            <img src="${data.video.cover || 'https://via.placeholder.com/200x300?text=No+Cover'}" 
                                 alt="封面" class="video-cover" onerror="this.src='https://via.placeholder.com/200x300?text=No+Cover'">
                        </div>
                        <div class="video-details">
                            <h3>${data.title || '无标题'}</h3>
                            <p><strong>平台：</strong>${getPlatformName(data.platform)}</p>
                            <p><strong>作者：</strong>${data.author.name || '未知'}</p>
                            <p><strong>类型：</strong>${isImage ? '图集' : '视频'}</p>
                            ${data.description ? `<p><strong>描述：</strong>${data.description}</p>` : ''}
                            ${data.statistics.like_count ? `<p><strong>点赞：</strong>${data.statistics.like_count}</p>` : ''}
            `;
            
            if (isImage && data.images.length > 0) {
                content += `<p><strong>图片数量：</strong>${data.images.length}张</p>`;
                content += `<a href="${data.images[0]}" target="_blank" class="video-link">查看第一张图片</a>`;
            } else if (videoUrl && !videoUrl.includes('当前为图文解析')) {
                content += `<a href="${videoUrl}" target="_blank" class="video-link">下载无水印视频</a>`;
            }
            
            content += `
                        </div>
                    </div>
                </div>
            `;
            
            resultDiv.innerHTML = content;
        }
        
        function showError(message) {
            resultDiv.innerHTML = `
                <div class="result error">
                    <h3>❌ 解析失败</h3>
                    <p>${message}</p>
                </div>
            `;
        }
        
        function getPlatformName(platform) {
            const names = {
                'douyin': '抖音',
                'kuaishou': '快手',
                'xiaohongshu': '小红书',
                'bilibili': '哔哩哔哩',
                'weibo': '微博'
            };
            return names[platform] || platform;
        }
        
        function setUrl(url) {
            document.getElementById('videoUrl').value = url;
        }
    </script>
</body>
</html>
