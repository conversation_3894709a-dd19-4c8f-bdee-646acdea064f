# 宝塔面板Nginx伪静态配置
# 在网站设置 -> 伪静态 中添加以下规则

# 隐藏PHP扩展名，美化URL
location / {
    try_files $uri $uri/ $uri.php?$query_string;
}

# API路由重写
location ~ ^/api/(.*)$ {
    try_files $uri $uri/ /index.php?$query_string;
}

# 批量处理路由
location /batch {
    try_files $uri $uri/ /index.php?$query_string;
}

# 状态监控路由
location /status {
    try_files $uri $uri/ /admin/status.php?$query_string;
}

# 管理面板路由
location /dashboard {
    try_files $uri $uri/ /admin/dashboard.php?$query_string;
}

# 安全配置 - 禁止访问敏感目录
location ~ ^/(config|logs|cache|cron|deploy|k8s|docker)/ {
    deny all;
    return 403;
}

# 禁止访问敏感文件
location ~ \.(env|git|svn|htaccess|htpasswd)$ {
    deny all;
    return 403;
}

# 禁止访问备份文件
location ~ \.(bak|backup|sql|tar|gz|zip)$ {
    deny all;
    return 403;
}

# PHP文件处理
location ~ \.php$ {
    fastcgi_pass unix:/tmp/php-cgi-80.sock;  # 根据PHP版本调整
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}

# 静态文件缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Access-Control-Allow-Origin "*";
    access_log off;
}

# JSON文件缓存
location ~* \.json$ {
    expires 1h;
    add_header Cache-Control "public";
    add_header Access-Control-Allow-Origin "*";
}

# 防盗链配置（可选）
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    valid_referers none blocked server_names
                   *.yourdomain.com yourdomain.com
                   *.google.com *.baidu.com;
    if ($invalid_referer) {
        return 403;
    }
}

# 限制请求方法
if ($request_method !~ ^(GET|HEAD|POST|OPTIONS)$ ) {
    return 405;
}

# 处理OPTIONS请求（CORS预检）
location ~ ^/.*$ {
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-Key';
        add_header 'Access-Control-Max-Age' 1728000;
        add_header 'Content-Type' 'text/plain; charset=utf-8';
        add_header 'Content-Length' 0;
        return 204;
    }
}

# 速率限制（防止DDoS）
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req zone=api burst=20 nodelay;

# 连接限制
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
limit_conn conn_limit_per_ip 20;

# 请求体大小限制
client_max_body_size 10M;

# 超时设置
client_body_timeout 10s;
client_header_timeout 10s;
keepalive_timeout 30s;
send_timeout 10s;

# Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_proxied any;
gzip_comp_level 6;
gzip_types
    text/plain
    text/css
    text/xml
    text/javascript
    application/json
    application/javascript
    application/xml+rss
    application/atom+xml
    image/svg+xml;

# 日志格式
log_format api_log '$remote_addr - $remote_user [$time_local] '
                   '"$request" $status $body_bytes_sent '
                   '"$http_referer" "$http_user_agent" '
                   '$request_time $upstream_response_time';

access_log /www/wwwlogs/api_access.log api_log;
