<?php
/**
 * 汽水音乐解析器
 */

class QishuiMusicParser extends AbstractParser
{
    /**
     * 解析汽水音乐
     */
    public function parse($url)
    {
        try {
            $this->info("开始解析汽水音乐URL: $url");
            
            // 获取重定向后的URL
            $finalUrl = $this->getRedirectUrl($url);
            $this->debug("重定向后的URL: $finalUrl");

            // 提取音乐ID
            $trackId = $this->extractVideoId($finalUrl);
            if (!$trackId) {
                return $this->handleError('无法提取音乐ID');
            }

            $this->debug("提取到音乐ID: $trackId");

            // 获取音乐信息
            $musicInfo = $this->getMusicInfo($trackId);
            if (!$musicInfo) {
                return $this->handleError('获取音乐信息失败');
            }

            // 格式化数据
            $formattedData = $this->formatData($musicInfo);
            $formattedData['source_url'] = $url;

            $this->info("汽水音乐解析成功");
            return $formattedData;

        } catch (Exception $e) {
            return $this->handleError('解析过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * 获取重定向URL
     */
    private function getRedirectUrl($url)
    {
        if (strpos($url, 'qishui.douyin.com') !== false) {
            $headers = get_headers($url, true, stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]));
            
            if (isset($headers['Location'])) {
                return is_array($headers['Location']) ? end($headers['Location']) : $headers['Location'];
            }
        }
        
        return $url;
    }

    /**
     * 提取音乐ID
     */
    protected function extractVideoId($url)
    {
        if (preg_match('/track_id=(\d+)/', $url, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * 获取音乐信息
     */
    protected function getVideoInfo($trackId)
    {
        return $this->getMusicInfo($trackId);
    }

    /**
     * 获取音乐信息
     */
    private function getMusicInfo($trackId)
    {
        try {
            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ];

            $requestUrl = "https://music.douyin.com/qishui/share/track?track_id=" . $trackId;
            $response = $this->httpClient->get($requestUrl, $headers, 'desktop');
            
            if (!$response) {
                return null;
            }

            // 提取 application/ld+json 数据
            $title = '';
            $cover = '';
            if (preg_match('/<script data-react-helmet="true" type="application\/ld\+json">(.*?)<\/script>/s', $response, $ldJsonMatches)) {
                $ldJsonData = json_decode(urldecode($ldJsonMatches[1]), true);
                $title = $ldJsonData['title'] ?? '';
                $cover = isset($ldJsonData['images']) && count($ldJsonData['images']) > 0 ? $ldJsonData['images'][0] : '';
            }

            // 提取音乐URL和歌词
            $musicUrl = '';
            $lyrics = '';
            $jsJsonPattern = '/<script\s+async=""\s+data-script-src="modern-inline">_ROUTER_DATA\s*=\s*({[\s\S]*?});/';
            
            if (preg_match($jsJsonPattern, $response, $jsJsonMatches)) {
                $jsonStr = $jsJsonMatches[1];
                $jsonData = json_decode(trim($jsonStr), true);
                
                if ($jsonData !== null && isset($jsonData['loaderData']['track_page']['audioWithLyricsOption']['url'])) {
                    $musicUrl = $jsonData['loaderData']['track_page']['audioWithLyricsOption']['url'];
                }

                // 提取歌词
                if ($jsonData !== null && isset($jsonData['loaderData']['track_page']['audioWithLyricsOption']['lyrics']['sentences'])) {
                    $sentences = $jsonData['loaderData']['track_page']['audioWithLyricsOption']['lyrics']['sentences'];
                    $lrcLyrics = [];
                    
                    foreach ($sentences as $sentence) {
                        if (isset($sentence['startMs']) && isset($sentence['words'])) {
                            $startMs = $sentence['startMs'];
                            $sentenceText = '';
                            
                            foreach ($sentence['words'] as $word) {
                                if (isset($word['text'])) {
                                    $sentenceText .= $word['text'];
                                }
                            }
                            
                            // 转换为LRC格式
                            $minutes = floor($startMs / 60000);
                            $seconds = floor(($startMs % 60000) / 1000);
                            $milliseconds = $startMs % 1000;
                            $timeTag = sprintf("[%02d:%02d.%03d]", $minutes, $seconds, $milliseconds);
                            $lrcLyrics[] = $timeTag . $sentenceText;
                        }
                    }
                    
                    $lyrics = implode("\n", $lrcLyrics);
                }
            }

            // 构造标准化数据
            return [
                'title' => $title,
                'desc' => $title,
                'author' => '汽水音乐',
                'author_id' => '',
                'avatar' => '',
                'cover' => $cover,
                'url' => $musicUrl,
                'music' => [
                    'title' => $title,
                    'url' => $musicUrl,
                    'cover' => $cover,
                    'lyrics' => $lyrics
                ],
                'lyrics' => $lyrics,
                'type' => 'music'
            ];

        } catch (Exception $e) {
            $this->debug("获取汽水音乐信息失败: " . $e->getMessage());
            return null;
        }
    }
}
?>
