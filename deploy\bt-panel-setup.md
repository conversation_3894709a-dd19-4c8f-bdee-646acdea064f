# 宝塔面板环境配置指南

## 1. 安装必需软件

### 1.1 安装PHP
1. 登录宝塔面板
2. 点击 **软件商店**
3. 搜索 **PHP**，选择 **PHP 8.0** 或 **PHP 8.1**
4. 点击 **安装**，等待安装完成

### 1.2 安装PHP扩展
安装完PHP后，点击PHP版本后的 **设置** 按钮：

**必需扩展：**
- ✅ curl
- ✅ json  
- ✅ mbstring
- ✅ openssl
- ✅ fileinfo
- ✅ opcache

**可选扩展（用于高级功能）：**
- ✅ redis (用于缓存)
- ✅ memcached (用于缓存)
- ✅ mysqli (用于数据库)
- ✅ pdo_mysql (用于数据库)

### 1.3 安装Nginx
1. 在软件商店搜索 **Nginx**
2. 选择 **Nginx 1.20+** 版本
3. 点击安装

### 1.4 安装MySQL（可选）
如果需要数据库功能：
1. 搜索 **MySQL**
2. 选择 **MySQL 8.0** 版本
3. 点击安装
4. 记住root密码

### 1.5 安装Redis（可选）
如果需要Redis缓存：
1. 搜索 **Redis**
2. 点击安装
3. 启动Redis服务

## 2. PHP配置优化

### 2.1 修改PHP配置
点击PHP版本的 **设置** → **配置修改**：

```ini
# 内存限制
memory_limit = 512M

# 执行时间限制
max_execution_time = 60
max_input_time = 60

# 文件上传
upload_max_filesize = 50M
post_max_size = 50M

# 错误报告（生产环境建议关闭）
display_errors = Off
log_errors = On

# OPcache优化
opcache.enable = 1
opcache.memory_consumption = 256
opcache.max_accelerated_files = 20000
opcache.validate_timestamps = 0
opcache.save_comments = 1
```

### 2.2 禁用危险函数
在 **禁用函数** 中，确保以下函数没有被禁用：
- curl_exec
- curl_init
- file_get_contents
- json_decode
- json_encode

## 3. 创建网站

### 3.1 添加站点
1. 点击 **网站** → **添加站点**
2. 填写域名（如：api.yourdomain.com）
3. 选择PHP版本（刚安装的PHP 8.0+）
4. 创建数据库（可选）
5. 点击 **提交**

### 3.2 配置SSL证书（推荐）
1. 点击域名后的 **设置**
2. 选择 **SSL** 标签
3. 申请Let's Encrypt免费证书
4. 开启 **强制HTTPS**

## 4. 安全配置

### 4.1 防火墙设置
在 **安全** → **防火墙** 中：
- 开放80端口（HTTP）
- 开放443端口（HTTPS）
- 关闭不必要的端口

### 4.2 IP访问限制（可选）
如果需要限制访问：
1. 在网站设置中选择 **访问限制**
2. 添加允许的IP地址

## 5. 性能优化

### 5.1 开启Gzip压缩
在网站设置的 **性能优化** 中：
- 开启 **Gzip压缩**
- 开启 **静态文件缓存**

### 5.2 配置缓存规则
添加缓存规则：
```
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 6. 监控配置

### 6.1 开启系统监控
在 **监控** 中开启：
- CPU监控
- 内存监控
- 磁盘监控
- 网络监控

### 6.2 设置告警
配置告警阈值：
- CPU使用率 > 80%
- 内存使用率 > 80%
- 磁盘使用率 > 90%

## 7. 备份配置

### 7.1 自动备份
在 **计划任务** 中添加：
- 网站备份（每天）
- 数据库备份（每天）
- 日志备份（每周）

### 7.2 备份存储
配置备份存储：
- 本地存储
- 云存储（阿里云OSS/腾讯云COS等）

## 8. 日志管理

### 8.1 访问日志
开启Nginx访问日志：
```
access_log /www/wwwlogs/api.yourdomain.com.log;
```

### 8.2 错误日志
开启PHP错误日志：
```
log_errors = On
error_log = /www/wwwlogs/php_errors.log
```

## 9. 定时任务配置

在 **计划任务** 中添加维护任务：

```bash
# 每小时清理缓存
0 * * * * cd /www/wwwroot/api.yourdomain.com && php cron/maintenance.php

# 每天生成报告
0 8 * * * cd /www/wwwroot/api.yourdomain.com && php cron/generate_report.php
```

## 10. 常见问题解决

### 10.1 权限问题
```bash
# 设置正确的文件权限
chown -R www:www /www/wwwroot/api.yourdomain.com
chmod -R 755 /www/wwwroot/api.yourdomain.com
chmod -R 777 /www/wwwroot/api.yourdomain.com/cache
chmod -R 777 /www/wwwroot/api.yourdomain.com/logs
```

### 10.2 PHP扩展问题
如果提示缺少扩展：
1. 在PHP设置中安装对应扩展
2. 重启PHP服务

### 10.3 内存不足
如果出现内存不足：
1. 增加PHP内存限制
2. 优化代码逻辑
3. 升级服务器配置

### 10.4 网络超时
如果请求超时：
1. 增加PHP执行时间限制
2. 优化网络连接
3. 使用CDN加速
