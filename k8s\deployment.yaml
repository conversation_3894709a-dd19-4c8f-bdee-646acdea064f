apiVersion: apps/v1
kind: Deployment
metadata:
  name: video-parser-app
  labels:
    app: video-parser
    component: app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: video-parser
      component: app
  template:
    metadata:
      labels:
        app: video-parser
        component: app
    spec:
      containers:
      - name: app
        image: video-parser:latest
        ports:
        - containerPort: 80
        env:
        - name: DB_HOST
          value: "mysql-service"
        - name: DB_NAME
          value: "video_parser"
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: username
        - name: DB_PASS
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: password
        - name: REDIS_HOST
          value: "redis-service"
        - name: CACHE_ENABLE
          value: "true"
        - name: DB_ENABLE
          value: "true"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /admin/status.php
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /admin/status.php
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: cache-volume
          mountPath: /var/www/html/cache
        - name: logs-volume
          mountPath: /var/www/html/logs
      volumes:
      - name: cache-volume
        emptyDir: {}
      - name: logs-volume
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: video-parser-service
  labels:
    app: video-parser
spec:
  selector:
    app: video-parser
    component: app
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: video-parser-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.yourdomain.com
    secretName: tls-secret
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: video-parser-service
            port:
              number: 80

---
apiVersion: v1
kind: Secret
metadata:
  name: mysql-secret
type: Opaque
data:
  username: YXBwX3VzZXI=  # app_user (base64)
  password: YXBwX3Bhc3N3b3Jk  # app_password (base64)

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: video-parser-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: video-parser-app
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
