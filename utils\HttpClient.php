<?php
/**
 * HTTP客户端工具类
 */

class HttpClient
{
    private $timeout;
    private $maxRedirects;
    private $userAgents;

    public function __construct()
    {
        $this->timeout = defined('REQUEST_TIMEOUT') ? REQUEST_TIMEOUT : 30;
        $this->maxRedirects = defined('MAX_REDIRECTS') ? MAX_REDIRECTS : 5;
        $this->userAgents = function_exists('getUserAgents') ? getUserAgents() : [
            'mobile' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
            'desktop' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        ];
    }

    /**
     * 发送GET请求
     */
    public function get($url, $headers = [], $userAgentType = 'mobile')
    {
        return $this->request('GET', $url, null, $headers, $userAgentType);
    }

    /**
     * 发送POST请求
     */
    public function post($url, $data = null, $headers = [], $userAgentType = 'mobile')
    {
        return $this->request('POST', $url, $data, $headers, $userAgentType);
    }

    /**
     * 获取重定向后的URL
     */
    public function getRedirectUrl($url)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_NOBODY => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_USERAGENT => $this->userAgents['mobile']
        ]);

        curl_exec($ch);
        $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL error: $error");
        }

        return $finalUrl;
    }

    /**
     * 获取响应头信息
     */
    public function getHeaders($url)
    {
        // 创建上下文，处理SSL验证问题
        $context = stream_context_create([
            "http" => [
                "timeout" => $this->timeout,
                "user_agent" => $this->userAgents['mobile'],
                "follow_location" => true,
                "max_redirects" => $this->maxRedirects
            ],
            "ssl" => [
                "verify_peer" => false,
                "verify_peer_name" => false,
                "allow_self_signed" => true
            ]
        ]);

        $headers = get_headers($url, true, $context);
        if ($headers === false) {
            // 如果get_headers失败，尝试使用cURL
            return $this->getHeadersWithCurl($url);
        }
        return $headers;
    }

    /**
     * 使用cURL获取响应头
     */
    private function getHeadersWithCurl($url)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_NOBODY => true,
            CURLOPT_HEADER => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => $this->maxRedirects,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => $this->userAgents['mobile']
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL error: $error");
        }

        // 解析响应头
        $headers = [];
        $headerLines = explode("\r\n", $response);
        foreach ($headerLines as $line) {
            if (strpos($line, ':') !== false) {
                list($key, $value) = explode(':', $line, 2);
                $headers[trim($key)] = trim($value);
            }
        }

        return $headers;
    }

    /**
     * 通用请求方法
     */
    private function request($method, $url, $data = null, $headers = [], $userAgentType = 'mobile')
    {
        $ch = curl_init();
        
        $defaultHeaders = [
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate',
            'Connection: keep-alive',
            'Upgrade-Insecure-Requests: 1'
        ];

        $allHeaders = array_merge($defaultHeaders, $headers);

        $curlOptions = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => $this->maxRedirects,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_USERAGENT => $this->userAgents[$userAgentType] ?? $this->userAgents['mobile'],
            CURLOPT_HTTPHEADER => $allHeaders,
            CURLOPT_ENCODING => 'gzip, deflate'
        ];

        if ($method === 'POST') {
            $curlOptions[CURLOPT_POST] = true;
            if ($data) {
                $curlOptions[CURLOPT_POSTFIELDS] = $data;
            }
        }

        curl_setopt_array($ch, $curlOptions);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL error: $error");
        }

        if ($httpCode >= 400) {
            throw new Exception("HTTP error: $httpCode");
        }

        return $response;
    }



    /**
     * 提取URL中的ID
     */
    public function extractIdFromUrl($url, $pattern)
    {
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1] ?? $matches[0];
        }
        return null;
    }

    /**
     * 清理URL参数
     */
    public function cleanUrl($url)
    {
        $parsed = parse_url($url);
        
        $scheme = isset($parsed['scheme']) ? $parsed['scheme'] . '://' : '';
        $host = $parsed['host'] ?? '';
        $port = isset($parsed['port']) ? ':' . $parsed['port'] : '';
        $path = isset($parsed['path']) ? rawurldecode($parsed['path']) : '';
        $fragment = isset($parsed['fragment']) ? '#' . rawurldecode($parsed['fragment']) : '';

        // 处理国际化域名
        if (function_exists('idn_to_utf8') && preg_match('/^xn--/', $host)) {
            $host = idn_to_utf8($host, IDNA_DEFAULT, INTL_IDNA_VARIANT_UTS46);
        }

        // 移除认证信息
        $host = preg_replace('/^.*@/', '', $host);
        
        // 去掉路径末尾的斜杠
        $path = rtrim($path, '/');

        return $scheme . $host . $port . $path . $fragment;
    }
}
?>
