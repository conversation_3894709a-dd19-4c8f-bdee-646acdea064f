<?php
/**
 * 性能监控器
 * 监控API性能指标、资源使用情况等
 */

class PerformanceMonitor
{
    private $startTime;
    private $startMemory;
    private $checkpoints = [];
    private $metrics = [];
    private $logger;

    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
        $this->logger = new Logger();
    }

    /**
     * 添加性能检查点
     */
    public function checkpoint($name)
    {
        $this->checkpoints[$name] = [
            'time' => microtime(true),
            'memory' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
    }

    /**
     * 记录自定义指标
     */
    public function recordMetric($name, $value, $unit = '')
    {
        $this->metrics[$name] = [
            'value' => $value,
            'unit' => $unit,
            'timestamp' => microtime(true)
        ];
    }

    /**
     * 获取执行时间
     */
    public function getExecutionTime($checkpoint = null)
    {
        $endTime = $checkpoint ? $this->checkpoints[$checkpoint]['time'] : microtime(true);
        return round(($endTime - $this->startTime) * 1000, 2); // 毫秒
    }

    /**
     * 获取内存使用情况
     */
    public function getMemoryUsage($checkpoint = null)
    {
        $currentMemory = $checkpoint ? $this->checkpoints[$checkpoint]['memory'] : memory_get_usage(true);
        return [
            'current' => $this->formatBytes($currentMemory),
            'peak' => $this->formatBytes(memory_get_peak_usage(true)),
            'delta' => $this->formatBytes($currentMemory - $this->startMemory)
        ];
    }

    /**
     * 获取系统资源使用情况
     */
    public function getSystemMetrics()
    {
        $metrics = [
            'cpu_usage' => $this->getCPUUsage(),
            'memory_usage' => $this->getMemoryUsage(),
            'disk_usage' => $this->getDiskUsage(),
            'load_average' => $this->getLoadAverage(),
            'connection_count' => $this->getConnectionCount()
        ];

        return $metrics;
    }

    /**
     * 获取CPU使用率
     */
    private function getCPUUsage()
    {
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            return [
                '1min' => $load[0],
                '5min' => $load[1],
                '15min' => $load[2]
            ];
        }

        // Windows系统的CPU使用率获取
        if (PHP_OS_FAMILY === 'Windows') {
            $output = shell_exec('wmic cpu get loadpercentage /value');
            if (preg_match('/LoadPercentage=(\d+)/', $output, $matches)) {
                return ['current' => intval($matches[1])];
            }
        }

        return ['current' => 0];
    }

    /**
     * 获取磁盘使用情况
     */
    private function getDiskUsage()
    {
        $total = disk_total_space('.');
        $free = disk_free_space('.');
        $used = $total - $free;

        return [
            'total' => $this->formatBytes($total),
            'used' => $this->formatBytes($used),
            'free' => $this->formatBytes($free),
            'usage_percent' => round(($used / $total) * 100, 2)
        ];
    }

    /**
     * 获取负载平均值
     */
    private function getLoadAverage()
    {
        if (function_exists('sys_getloadavg')) {
            return sys_getloadavg();
        }
        return [0, 0, 0];
    }

    /**
     * 获取连接数
     */
    private function getConnectionCount()
    {
        // 这里可以根据实际情况获取数据库连接数、HTTP连接数等
        return [
            'active' => 0,
            'total' => 0
        ];
    }

    /**
     * 检查性能瓶颈
     */
    public function detectBottlenecks()
    {
        $bottlenecks = [];

        // 检查执行时间
        $executionTime = $this->getExecutionTime();
        if ($executionTime > 5000) { // 超过5秒
            $bottlenecks[] = [
                'type' => 'slow_execution',
                'message' => "执行时间过长: {$executionTime}ms",
                'severity' => 'high'
            ];
        }

        // 检查内存使用
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        if ($memoryUsage > $memoryLimit * 0.8) { // 超过80%
            $bottlenecks[] = [
                'type' => 'high_memory',
                'message' => "内存使用率过高: " . $this->formatBytes($memoryUsage),
                'severity' => 'medium'
            ];
        }

        // 检查磁盘空间
        $diskUsage = $this->getDiskUsage();
        if ($diskUsage['usage_percent'] > 90) {
            $bottlenecks[] = [
                'type' => 'disk_space',
                'message' => "磁盘空间不足: {$diskUsage['usage_percent']}%",
                'severity' => 'high'
            ];
        }

        return $bottlenecks;
    }

    /**
     * 生成性能报告
     */
    public function generateReport()
    {
        $report = [
            'timestamp' => date('Y-m-d H:i:s'),
            'execution_time' => $this->getExecutionTime(),
            'memory_usage' => $this->getMemoryUsage(),
            'system_metrics' => $this->getSystemMetrics(),
            'checkpoints' => $this->formatCheckpoints(),
            'custom_metrics' => $this->metrics,
            'bottlenecks' => $this->detectBottlenecks(),
            'recommendations' => $this->getRecommendations()
        ];

        return $report;
    }

    /**
     * 格式化检查点数据
     */
    private function formatCheckpoints()
    {
        $formatted = [];
        $lastTime = $this->startTime;
        $lastMemory = $this->startMemory;

        foreach ($this->checkpoints as $name => $checkpoint) {
            $formatted[$name] = [
                'elapsed_time' => round(($checkpoint['time'] - $this->startTime) * 1000, 2),
                'step_time' => round(($checkpoint['time'] - $lastTime) * 1000, 2),
                'memory_usage' => $this->formatBytes($checkpoint['memory']),
                'memory_delta' => $this->formatBytes($checkpoint['memory'] - $lastMemory)
            ];
            
            $lastTime = $checkpoint['time'];
            $lastMemory = $checkpoint['memory'];
        }

        return $formatted;
    }

    /**
     * 获取优化建议
     */
    private function getRecommendations()
    {
        $recommendations = [];
        $executionTime = $this->getExecutionTime();
        $memoryUsage = memory_get_usage(true);

        if ($executionTime > 3000) {
            $recommendations[] = "考虑启用缓存机制以减少响应时间";
            $recommendations[] = "优化数据库查询或外部API调用";
        }

        if ($memoryUsage > 50 * 1024 * 1024) { // 50MB
            $recommendations[] = "检查是否存在内存泄漏";
            $recommendations[] = "考虑使用流式处理大数据";
        }

        if (count($this->checkpoints) > 10) {
            $recommendations[] = "减少不必要的性能检查点";
        }

        return $recommendations;
    }

    /**
     * 记录性能日志
     */
    public function logPerformance($level = 'INFO')
    {
        $report = $this->generateReport();
        $message = sprintf(
            "Performance: %sms, Memory: %s, Bottlenecks: %d",
            $report['execution_time'],
            $report['memory_usage']['current'],
            count($report['bottlenecks'])
        );

        switch (strtoupper($level)) {
            case 'DEBUG':
                $this->logger->debug($message, $report);
                break;
            case 'WARN':
                $this->logger->warn($message, $report);
                break;
            case 'ERROR':
                $this->logger->error($message, $report);
                break;
            default:
                $this->logger->info($message, $report);
        }
    }

    /**
     * 格式化字节大小
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit($limit)
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;
        
        switch ($last) {
            case 'g':
                $limit *= 1024;
            case 'm':
                $limit *= 1024;
            case 'k':
                $limit *= 1024;
        }
        
        return $limit;
    }

    /**
     * 自动性能优化建议
     */
    public function autoOptimize()
    {
        $optimizations = [];

        // 检查OPcache
        if (!extension_loaded('opcache') || !opcache_get_status()['opcache_enabled']) {
            $optimizations[] = [
                'type' => 'opcache',
                'message' => '建议启用OPcache以提高PHP性能',
                'priority' => 'high'
            ];
        }

        // 检查内存限制
        $memoryLimit = ini_get('memory_limit');
        if ($memoryLimit !== '-1' && $this->parseMemoryLimit($memoryLimit) < 256 * 1024 * 1024) {
            $optimizations[] = [
                'type' => 'memory_limit',
                'message' => '建议增加PHP内存限制到256MB或更高',
                'priority' => 'medium'
            ];
        }

        // 检查执行时间限制
        $maxExecutionTime = ini_get('max_execution_time');
        if ($maxExecutionTime > 0 && $maxExecutionTime < 60) {
            $optimizations[] = [
                'type' => 'execution_time',
                'message' => '建议增加最大执行时间限制',
                'priority' => 'low'
            ];
        }

        return $optimizations;
    }
}
?>
