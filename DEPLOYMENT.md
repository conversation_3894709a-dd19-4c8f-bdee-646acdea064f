# 部署指南

## 📋 系统要求

### 基础环境
- **PHP**: 7.4 或更高版本
- **Web服务器**: Apache/Nginx
- **扩展**: cURL, JSON, mbstring
- **权限**: 可读写缓存和日志目录

### 推荐配置
- **内存**: 512MB 或更高
- **磁盘**: 1GB 可用空间
- **网络**: 稳定的外网连接

## 🚀 快速部署

### 1. 下载代码

```bash
# 克隆仓库
git clone <repository-url> unified_api
cd unified_api

# 或者直接下载ZIP包解压
```

### 2. 设置权限

```bash
# 设置基本权限
chmod 755 -R .

# 设置缓存和日志目录权限
chmod 777 cache/
chmod 777 logs/

# 如果目录不存在，创建它们
mkdir -p cache logs
chmod 777 cache logs
```

### 3. 配置Web服务器

#### Apache配置

创建 `.htaccess` 文件：

```apache
RewriteEngine On

# 隐藏PHP扩展名
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# 安全设置
<Files "config/*">
    Deny from all
</Files>

<Files "logs/*">
    Deny from all
</Files>

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/json "access plus 1 hour"
</IfModule>
```

#### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/unified_api;
    index index.php;

    # 隐藏PHP扩展名
    location / {
        try_files $uri $uri/ $uri.php?$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # 安全设置
    location ~ ^/(config|logs)/ {
        deny all;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 4. 配置参数

编辑 `config/config.php`：

```php
// 根据需要调整配置
define('CACHE_ENABLE', true);           // 启用缓存
define('CACHE_EXPIRE_TIME', 3600);      // 缓存1小时
define('LOG_ENABLE', true);             // 启用日志
define('LOG_LEVEL', 'INFO');            // 日志级别
define('REQUEST_TIMEOUT', 30);          // 请求超时30秒
```

### 5. 测试部署

```bash
# 命令行测试
php test/test.php http://your-domain.com/unified_api

# 或者访问Web测试页面
http://your-domain.com/unified_api/test/test.php
```

## 🔧 高级配置

### SSL/HTTPS配置

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 其他配置...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### 负载均衡配置

```nginx
upstream api_backend {
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
    server 127.0.0.1:8003;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 缓存优化

#### Redis缓存（可选）

如果需要使用Redis缓存，可以修改 `CacheManager.php`：

```php
class RedisCacheManager extends CacheManager
{
    private $redis;
    
    public function __construct()
    {
        $this->redis = new Redis();
        $this->redis->connect('127.0.0.1', 6379);
    }
    
    public function get($key)
    {
        return $this->redis->get($key);
    }
    
    public function set($key, $data, $expireTime = null)
    {
        return $this->redis->setex($key, $expireTime ?: CACHE_EXPIRE_TIME, $data);
    }
}
```

### 监控和日志

#### 日志轮转

创建 `logrotate` 配置：

```bash
# /etc/logrotate.d/unified_api
/path/to/unified_api/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

#### 系统监控

```bash
# 创建监控脚本
#!/bin/bash
# monitor.sh

API_URL="http://your-domain.com/unified_api/admin/status.php"
RESPONSE=$(curl -s "$API_URL")

if [[ $? -eq 0 ]]; then
    echo "API正常运行"
else
    echo "API异常，发送告警..."
    # 发送邮件或短信告警
fi
```

## 🛡️ 安全配置

### 1. 访问控制

```php
// 在index.php开头添加IP白名单
$allowedIPs = ['127.0.0.1', '***********/24'];
$clientIP = $_SERVER['REMOTE_ADDR'];

if (!in_array($clientIP, $allowedIPs)) {
    http_response_code(403);
    exit('Access Denied');
}
```

### 2. 速率限制

```php
// 启用速率限制
require_once 'utils/RateLimiter.php';

$rateLimiter = new RateLimiter(100, 3600); // 每小时100次
$clientIP = $_SERVER['REMOTE_ADDR'];

if (!$rateLimiter->isAllowed($clientIP)) {
    http_response_code(429);
    exit('Rate limit exceeded');
}
```

### 3. 防火墙规则

```bash
# UFW防火墙配置
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw enable
```

## 📊 性能优化

### 1. PHP优化

```ini
; php.ini优化
memory_limit = 512M
max_execution_time = 60
max_input_time = 60
post_max_size = 50M
upload_max_filesize = 50M

; OPcache配置
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
opcache.validate_timestamps=0
```

### 2. 数据库优化（如果使用）

```sql
-- 创建索引
CREATE INDEX idx_cache_key ON cache_table(cache_key);
CREATE INDEX idx_created_at ON cache_table(created_at);
```

### 3. CDN配置

```nginx
# 静态资源CDN
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Access-Control-Allow-Origin "*";
}
```

## 🔍 故障排除

### 常见问题

1. **权限错误**
```bash
chmod 777 cache/ logs/
chown -R www-data:www-data .
```

2. **内存不足**
```ini
memory_limit = 512M
```

3. **网络超时**
```php
define('REQUEST_TIMEOUT', 60);
```

4. **缓存问题**
```bash
rm -rf cache/*
```

### 调试模式

```php
// 在config.php中启用调试
define('DEBUG_MODE', true);
define('LOG_LEVEL', 'DEBUG');

// 显示错误信息
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}
```

## 📈 监控指标

### 关键指标
- API响应时间
- 成功率
- 错误率
- 缓存命中率
- 内存使用率
- 磁盘使用率

### 监控脚本

```bash
#!/bin/bash
# health_check.sh

# 检查API健康状态
curl -f http://localhost/unified_api/admin/status.php > /dev/null
if [ $? -ne 0 ]; then
    echo "API健康检查失败"
    exit 1
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "磁盘使用率过高: ${DISK_USAGE}%"
fi

echo "系统运行正常"
```

## 🔄 更新和维护

### 更新流程

1. **备份当前版本**
```bash
cp -r unified_api unified_api_backup_$(date +%Y%m%d)
```

2. **下载新版本**
```bash
git pull origin main
# 或下载新的ZIP包
```

3. **更新配置**
```bash
# 比较配置文件差异
diff config/config.php config/config.php.new
```

4. **测试新版本**
```bash
php test/test.php
```

5. **清理缓存**
```bash
rm -rf cache/*
```

### 定期维护

```bash
# 每日维护脚本
#!/bin/bash
# daily_maintenance.sh

# 清理过期缓存
curl -s "http://localhost/unified_api/admin/status.php?action=cleanup"

# 清理旧日志
find logs/ -name "*.log" -mtime +30 -delete

# 检查磁盘空间
df -h

echo "维护完成: $(date)"
```
