# 短视频去水印统一解析接口

## 📖 项目简介

这是一个统一的短视频去水印解析接口，支持多个主流平台的视频和图集内容解析。通过统一的API接口，您可以轻松解析来自不同平台的短视频链接，获取无水印的视频地址、封面图片、作者信息等详细数据。

## ✨ 特性

- 🎯 **统一接口** - 一个接口支持多个平台
- 🚀 **自动识别** - 智能检测URL所属平台
- 💾 **缓存机制** - 提高响应速度，减少重复请求
- 📊 **标准化数据** - 统一的数据格式，便于处理
- 🛡️ **错误处理** - 完善的异常处理和日志记录
- 📈 **监控面板** - 系统状态监控和管理
- 🔧 **易于扩展** - 模块化设计，便于添加新平台

## 🎬 支持平台

| 平台 | 状态 | 支持内容 | 备注 |
|------|------|----------|------|
| 抖音 | ✅ | 视频、图集 | 功能完整 |
| 快手 | ✅ | 视频、图集 | 已修复图集解析 |
| 小红书 | ✅ | 视频、图文 | 已优化视频解析 |
| 哔哩哔哩 | ✅ | 视频 | 功能完整 |
| 汽水音乐 | ✅ | 音乐 | 新增支持 |
| 网易云音乐 | ✅ | 音乐 | 新增支持 |
| 微博 | 🚧 | 视频 | 开发中 |
| 皮皮搞笑 | 🚧 | 视频 | 开发中 |
| 皮皮虾 | 🚧 | 视频 | 开发中 |

## 🚀 快速开始

### 环境要求

- PHP 7.4+
- cURL 扩展
- JSON 扩展
- 可写的缓存和日志目录

### 安装部署

1. **下载代码**
```bash
git clone <repository-url>
cd unified_api
```

2. **配置权限**
```bash
chmod 755 -R .
chmod 777 cache/ logs/
```

3. **配置Web服务器**

将项目部署到Web服务器，确保 `index.php` 可以通过HTTP访问。

### 基本使用

#### 解析视频

```http
GET /index.php?url=https://v.douyin.com/xxxxxx
```

#### 响应格式

```json
{
    "code": 200,
    "msg": "解析成功",
    "data": {
        "platform": "douyin",
        "type": "video",
        "title": "视频标题",
        "description": "视频描述",
        "author": {
            "name": "作者名称",
            "id": "作者ID",
            "avatar": "头像URL"
        },
        "video": {
            "url": "无水印视频URL",
            "cover": "封面图URL",
            "duration": 15,
            "width": 720,
            "height": 1280
        },
        "images": [],
        "music": {
            "title": "音乐标题",
            "author": "音乐作者",
            "url": "音乐URL",
            "cover": "音乐封面"
        },
        "statistics": {
            "like_count": 1000,
            "comment_count": 100,
            "share_count": 50,
            "view_count": 10000
        },
        "create_time": "2025-07-23 10:30:00",
        "source_url": "原始URL"
    },
    "timestamp": 1721721000,
    "api_version": "1.0.0"
}
```

## 📚 API 文档

### 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| url | string | 是 | 短视频平台的分享链接 |

### 响应状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 解析成功 |
| 400 | 请求参数错误 |
| 404 | 解析失败或内容不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

### 数据字段说明

#### 基础字段
- `platform`: 平台标识
- `type`: 内容类型（video/image）
- `title`: 标题
- `description`: 描述
- `create_time`: 创建时间
- `source_url`: 原始URL

#### 作者信息 (author)
- `name`: 作者名称
- `id`: 作者ID
- `avatar`: 头像URL

#### 视频信息 (video)
- `url`: 视频播放地址（无水印）
- `cover`: 封面图片
- `duration`: 时长（秒）
- `width`: 宽度
- `height`: 高度

#### 图片信息 (images)
图片URL数组，用于图集类型内容

#### 音乐信息 (music)
- `title`: 音乐标题
- `author`: 音乐作者
- `url`: 音乐地址
- `cover`: 音乐封面

#### 统计信息 (statistics)
- `like_count`: 点赞数
- `comment_count`: 评论数
- `share_count`: 分享数
- `view_count`: 播放数

## 🔧 配置说明

### 基础配置 (config/config.php)

```php
// 缓存配置
define('CACHE_ENABLE', true);
define('CACHE_EXPIRE_TIME', 3600);

// 日志配置
define('LOG_ENABLE', true);
define('LOG_LEVEL', 'INFO');

// 请求配置
define('REQUEST_TIMEOUT', 30);
```

### 添加新平台

1. **更新配置**
在 `config/config.php` 中添加平台配置：

```php
$SUPPORTED_PLATFORMS['newplatform'] = [
    'name' => '新平台',
    'domains' => ['newplatform.com'],
    'class' => 'NewPlatformParser'
];
```

2. **创建解析器**
在 `core/parsers/` 目录下创建 `NewPlatformParser.php`：

```php
class NewPlatformParser extends AbstractParser
{
    public function parse($url) {
        // 实现解析逻辑
    }
    
    protected function extractVideoId($url) {
        // 实现ID提取逻辑
    }
    
    protected function getVideoInfo($videoId) {
        // 实现信息获取逻辑
    }
}
```

## 📊 监控管理

### 系统状态

访问 `/admin/status.php` 查看系统状态：

```http
GET /admin/status.php
```

### 系统清理

清理过期缓存和日志：

```http
GET /admin/status.php?action=cleanup
```

## 🧪 测试

### 测试用例

```bash
# 测试抖音视频
curl "http://your-domain/index.php?url=https://v.douyin.com/xxxxxx"

# 测试快手视频
curl "http://your-domain/index.php?url=https://www.kuaishou.com/short-video/xxxxxx"

# 测试小红书
curl "http://your-domain/index.php?url=http://xhslink.com/a/xxxxxx"

# 测试B站视频
curl "http://your-domain/index.php?url=https://www.bilibili.com/video/BVxxxxxx"
```

## 🛠️ 故障排除

### 常见问题

1. **解析失败**
   - 检查URL格式是否正确
   - 确认平台是否支持
   - 查看日志文件获取详细错误信息

2. **缓存问题**
   - 确保缓存目录可写
   - 检查磁盘空间是否充足

3. **网络问题**
   - 检查服务器网络连接
   - 确认目标平台是否可访问

### 日志查看

日志文件位于 `logs/` 目录下，按日期命名：

```bash
tail -f logs/2025-07-23.log
```

## 📄 许可证

本项目遵循 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

## 📞 联系方式

- 作者：JH-Ahua (Enhanced by AI)
- 邮箱：<EMAIL>
- 网站：https://api.bugpk.com/
