# 短视频去水印统一解析接口 - 定时任务配置
# 复制此文件内容到系统crontab中: crontab -e

# 设置环境变量
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=<EMAIL>

# 项目根目录（请根据实际情况修改）
PROJECT_ROOT=/var/www/html/unified_api

# ============================================================================
# 系统维护任务
# ============================================================================

# 每小时执行一次系统维护（清理缓存、检查告警等）
0 * * * * cd $PROJECT_ROOT && php cron/maintenance.php >> logs/cron.log 2>&1

# 每天凌晨2点执行完整维护（清理日志、优化数据库等）
0 2 * * * cd $PROJECT_ROOT && php cron/maintenance.php --full >> logs/cron.log 2>&1

# ============================================================================
# 监控和告警
# ============================================================================

# 每5分钟检查一次系统告警
*/5 * * * * cd $PROJECT_ROOT && php -r "require 'config/config.php'; require 'core/AlertManager.php'; (new AlertManager())->checkAlerts();" >> logs/alert.log 2>&1

# 每15分钟检查一次API健康状态
*/15 * * * * curl -f http://localhost/unified_api/admin/status.php > /dev/null 2>&1 || echo "API健康检查失败 $(date)" >> $PROJECT_ROOT/logs/health.log

# ============================================================================
# 数据备份
# ============================================================================

# 每天凌晨3点备份数据库（如果启用了数据库）
0 3 * * * mysqldump -u root -p'password' video_parser > $PROJECT_ROOT/backup/db_backup_$(date +\%Y\%m\%d).sql 2>> $PROJECT_ROOT/logs/backup.log

# 每周日凌晨4点备份整个项目
0 4 * * 0 tar -czf $PROJECT_ROOT/backup/project_backup_$(date +\%Y\%m\%d).tar.gz -C /var/www/html unified_api 2>> $PROJECT_ROOT/logs/backup.log

# ============================================================================
# 日志管理
# ============================================================================

# 每天凌晨1点轮转日志文件
0 1 * * * cd $PROJECT_ROOT && find logs/ -name "*.log" -size +100M -exec mv {} {}.$(date +\%Y\%m\%d) \; 2>> logs/logrotate.log

# 每周清理超过30天的日志文件
0 1 * * 0 cd $PROJECT_ROOT && find logs/ -name "*.log.*" -mtime +30 -delete 2>> logs/cleanup.log

# ============================================================================
# 性能优化
# ============================================================================

# 每小时清理过期缓存
30 * * * * cd $PROJECT_ROOT && php -r "require 'config/config.php'; require 'core/CacheManager.php'; (new CacheManager())->cleanExpired();" >> logs/cache.log 2>&1

# 每天凌晨优化数据库表（如果启用了数据库）
0 5 * * * mysql -u root -p'password' video_parser -e "OPTIMIZE TABLE api_statistics, access_logs, parse_records;" 2>> $PROJECT_ROOT/logs/optimize.log

# ============================================================================
# 安全维护
# ============================================================================

# 每小时清理过期的IP封禁
0 * * * * cd $PROJECT_ROOT && php -r "require 'config/config.php'; require 'core/SecurityManager.php'; (new SecurityManager())->cleanupExpiredBlocks();" >> logs/security.log 2>&1

# 每天检查可疑活动
0 6 * * * cd $PROJECT_ROOT && grep -i "suspicious\|malicious\|blocked" logs/$(date +\%Y-\%m-\%d).log | wc -l >> logs/security_summary.log

# ============================================================================
# 报告生成
# ============================================================================

# 每天早上8点生成日报
0 8 * * * cd $PROJECT_ROOT && php cron/generate_report.php --type=daily >> logs/report.log 2>&1

# 每周一早上8点生成周报
0 8 * * 1 cd $PROJECT_ROOT && php cron/generate_report.php --type=weekly >> logs/report.log 2>&1

# 每月1号早上8点生成月报
0 8 1 * * cd $PROJECT_ROOT && php cron/generate_report.php --type=monthly >> logs/report.log 2>&1

# ============================================================================
# 系统检查
# ============================================================================

# 每小时检查磁盘空间
0 * * * * df -h | awk '$5 > 90 {print "磁盘空间不足: " $0}' >> $PROJECT_ROOT/logs/disk.log

# 每天检查系统负载
0 */6 * * * uptime >> $PROJECT_ROOT/logs/load.log

# 每天检查内存使用
0 */6 * * * free -h >> $PROJECT_ROOT/logs/memory.log

# ============================================================================
# 自定义任务示例
# ============================================================================

# 每天凌晨预热热门内容缓存
# 0 0 * * * cd $PROJECT_ROOT && php cron/cache_warmup.php >> logs/warmup.log 2>&1

# 每周统计平台使用情况
# 0 9 * * 1 cd $PROJECT_ROOT && php cron/platform_stats.php >> logs/stats.log 2>&1

# 每月清理无效的解析记录
# 0 2 1 * * cd $PROJECT_ROOT && php cron/cleanup_invalid_records.php >> logs/cleanup.log 2>&1

# ============================================================================
# 注意事项
# ============================================================================

# 1. 请根据实际情况修改 PROJECT_ROOT 路径
# 2. 请根据实际情况修改数据库连接信息
# 3. 请确保 PHP 可执行文件在 PATH 中
# 4. 请确保相关目录有写权限
# 5. 建议定期检查日志文件，确保任务正常执行
# 6. 可以根据服务器负载调整任务执行频率

# 安装方法：
# 1. 复制此文件内容
# 2. 执行 crontab -e
# 3. 粘贴内容并保存
# 4. 执行 crontab -l 验证安装成功

# 查看定时任务日志：
# tail -f /var/www/html/unified_api/logs/cron.log
