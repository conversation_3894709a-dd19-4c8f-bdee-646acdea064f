<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频解析API - 监控面板</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
        .container { max-width: 1200px; margin: 20px auto; padding: 0 20px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .card { background: white; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card h3 { color: #333; margin-bottom: 15px; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
        .metric { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid #eee; }
        .metric:last-child { border-bottom: none; }
        .metric-value { font-weight: bold; color: #667eea; }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .chart-container { height: 300px; margin-top: 15px; }
        .log-container { max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; }
        .refresh-btn { background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-bottom: 20px; }
        .refresh-btn:hover { background: #5a6fd8; }
        .alert { padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .platform-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; }
        .platform-card { text-align: center; padding: 15px; background: #f8f9fa; border-radius: 8px; }
        .platform-card h4 { color: #333; margin-bottom: 10px; }
        .platform-card .count { font-size: 24px; font-weight: bold; color: #667eea; }
        .platform-card .rate { font-size: 12px; color: #666; }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="header">
        <h1>📊 视频解析API监控面板</h1>
        <p>实时监控系统状态和性能指标</p>
    </div>

    <div class="container">
        <button class="refresh-btn" onclick="refreshData()">🔄 刷新数据</button>
        
        <div id="alerts"></div>

        <div class="grid">
            <!-- 系统状态 -->
            <div class="card">
                <h3>🖥️ 系统状态</h3>
                <div id="system-status">
                    <div class="metric">
                        <span>API状态</span>
                        <span class="metric-value status-good" id="api-status">运行中</span>
                    </div>
                    <div class="metric">
                        <span>运行时间</span>
                        <span class="metric-value" id="uptime">-</span>
                    </div>
                    <div class="metric">
                        <span>PHP版本</span>
                        <span class="metric-value" id="php-version">-</span>
                    </div>
                    <div class="metric">
                        <span>内存使用</span>
                        <span class="metric-value" id="memory-usage">-</span>
                    </div>
                    <div class="metric">
                        <span>磁盘使用</span>
                        <span class="metric-value" id="disk-usage">-</span>
                    </div>
                </div>
            </div>

            <!-- 缓存状态 -->
            <div class="card">
                <h3>💾 缓存状态</h3>
                <div id="cache-status">
                    <div class="metric">
                        <span>缓存状态</span>
                        <span class="metric-value" id="cache-enabled">-</span>
                    </div>
                    <div class="metric">
                        <span>缓存文件数</span>
                        <span class="metric-value" id="cache-files">-</span>
                    </div>
                    <div class="metric">
                        <span>缓存大小</span>
                        <span class="metric-value" id="cache-size">-</span>
                    </div>
                    <div class="metric">
                        <span>命中率</span>
                        <span class="metric-value" id="cache-hit-rate">-</span>
                    </div>
                </div>
            </div>

            <!-- API统计 -->
            <div class="card">
                <h3>📈 API统计</h3>
                <div id="api-stats">
                    <div class="metric">
                        <span>今日请求</span>
                        <span class="metric-value" id="today-requests">-</span>
                    </div>
                    <div class="metric">
                        <span>成功率</span>
                        <span class="metric-value" id="success-rate">-</span>
                    </div>
                    <div class="metric">
                        <span>平均响应时间</span>
                        <span class="metric-value" id="avg-response-time">-</span>
                    </div>
                    <div class="metric">
                        <span>错误数</span>
                        <span class="metric-value" id="error-count">-</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 平台统计 -->
        <div class="card">
            <h3>🎬 平台解析统计</h3>
            <div class="platform-stats" id="platform-stats">
                <!-- 动态生成 -->
            </div>
        </div>

        <!-- 性能图表 -->
        <div class="card">
            <h3>📊 性能趋势</h3>
            <div class="chart-container">
                <canvas id="performance-chart"></canvas>
            </div>
        </div>

        <!-- 最近日志 -->
        <div class="card">
            <h3>📝 最近日志</h3>
            <div class="log-container" id="recent-logs">
                加载中...
            </div>
        </div>
    </div>

    <script>
        let performanceChart;
        
        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '响应时间 (ms)',
                        data: [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }, {
                        label: '请求数',
                        data: [],
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        yAxisID: 'y1',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }

        // 刷新数据
        async function refreshData() {
            try {
                const response = await fetch('status.php');
                const data = await response.json();
                
                updateSystemStatus(data);
                updateCacheStatus(data);
                updateApiStats(data);
                updatePlatformStats(data);
                updateChart(data);
                updateLogs(data);
                checkAlerts(data);
                
            } catch (error) {
                console.error('刷新数据失败:', error);
                showAlert('数据刷新失败: ' + error.message, 'danger');
            }
        }

        // 更新系统状态
        function updateSystemStatus(data) {
            const system = data.system_info || {};
            
            document.getElementById('php-version').textContent = system.php_version || '-';
            document.getElementById('memory-usage').textContent = formatBytes(system.memory_usage?.current || 0);
            document.getElementById('disk-usage').textContent = formatBytes(system.disk_space?.total - system.disk_space?.free || 0);
            
            // 计算运行时间
            const uptime = data.api_info?.uptime || 0;
            document.getElementById('uptime').textContent = formatUptime(uptime);
        }

        // 更新缓存状态
        function updateCacheStatus(data) {
            const cache = data.cache_stats || {};
            
            document.getElementById('cache-enabled').textContent = cache.enabled ? '已启用' : '已禁用';
            document.getElementById('cache-files').textContent = cache.total_files || 0;
            document.getElementById('cache-size').textContent = cache.total_size_formatted || '-';
            
            const hitRate = cache.hit_ratio?.memory || 0;
            document.getElementById('cache-hit-rate').textContent = hitRate + '%';
        }

        // 更新API统计
        function updateApiStats(data) {
            const stats = data.statistics || [];
            
            let totalRequests = 0;
            let successRequests = 0;
            let totalResponseTime = 0;
            
            stats.forEach(stat => {
                totalRequests += stat.total_requests || 0;
                successRequests += stat.success_requests || 0;
                totalResponseTime += stat.avg_response_time || 0;
            });
            
            document.getElementById('today-requests').textContent = totalRequests;
            document.getElementById('success-rate').textContent = totalRequests > 0 ? 
                Math.round((successRequests / totalRequests) * 100) + '%' : '0%';
            document.getElementById('avg-response-time').textContent = 
                Math.round(totalResponseTime / (stats.length || 1)) + 'ms';
            document.getElementById('error-count').textContent = totalRequests - successRequests;
        }

        // 更新平台统计
        function updatePlatformStats(data) {
            const stats = data.statistics || [];
            const container = document.getElementById('platform-stats');
            
            container.innerHTML = '';
            
            stats.forEach(stat => {
                const platformCard = document.createElement('div');
                platformCard.className = 'platform-card';
                
                const successRate = stat.total_requests > 0 ? 
                    Math.round((stat.success_requests / stat.total_requests) * 100) : 0;
                
                platformCard.innerHTML = `
                    <h4>${getPlatformName(stat.platform)}</h4>
                    <div class="count">${stat.total_requests || 0}</div>
                    <div class="rate">成功率: ${successRate}%</div>
                `;
                
                container.appendChild(platformCard);
            });
        }

        // 更新图表
        function updateChart(data) {
            // 这里可以添加实时数据更新逻辑
            // 暂时使用模拟数据
            const now = new Date();
            const timeLabel = now.getHours() + ':' + String(now.getMinutes()).padStart(2, '0');
            
            if (performanceChart.data.labels.length > 20) {
                performanceChart.data.labels.shift();
                performanceChart.data.datasets[0].data.shift();
                performanceChart.data.datasets[1].data.shift();
            }
            
            performanceChart.data.labels.push(timeLabel);
            performanceChart.data.datasets[0].data.push(Math.random() * 1000 + 200);
            performanceChart.data.datasets[1].data.push(Math.random() * 100 + 10);
            
            performanceChart.update();
        }

        // 更新日志
        function updateLogs(data) {
            const logs = data.recent_logs?.logs || [];
            const container = document.getElementById('recent-logs');
            
            if (logs.length > 0) {
                container.innerHTML = logs.slice(-20).join('\n');
                container.scrollTop = container.scrollHeight;
            } else {
                container.innerHTML = '暂无日志';
            }
        }

        // 检查告警
        function checkAlerts(data) {
            const alerts = [];
            
            // 检查内存使用
            const memoryUsage = data.system_info?.memory_usage?.current || 0;
            if (memoryUsage > 100 * 1024 * 1024) { // 100MB
                alerts.push({
                    type: 'warning',
                    message: '内存使用量较高: ' + formatBytes(memoryUsage)
                });
            }
            
            // 检查错误率
            const stats = data.statistics || [];
            stats.forEach(stat => {
                const errorRate = stat.total_requests > 0 ? 
                    ((stat.total_requests - stat.success_requests) / stat.total_requests) * 100 : 0;
                
                if (errorRate > 10) {
                    alerts.push({
                        type: 'danger',
                        message: `${getPlatformName(stat.platform)} 错误率过高: ${errorRate.toFixed(1)}%`
                    });
                }
            });
            
            showAlerts(alerts);
        }

        // 显示告警
        function showAlerts(alerts) {
            const container = document.getElementById('alerts');
            container.innerHTML = '';
            
            alerts.forEach(alert => {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${alert.type}`;
                alertDiv.textContent = alert.message;
                container.appendChild(alertDiv);
            });
        }

        // 显示单个告警
        function showAlert(message, type = 'warning') {
            const container = document.getElementById('alerts');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            container.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }

        // 工具函数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            if (days > 0) return `${days}天 ${hours}小时`;
            if (hours > 0) return `${hours}小时 ${minutes}分钟`;
            return `${minutes}分钟`;
        }

        function getPlatformName(platform) {
            const names = {
                'douyin': '抖音',
                'kuaishou': '快手',
                'xiaohongshu': '小红书',
                'bilibili': '哔哩哔哩',
                'weibo': '微博'
            };
            return names[platform] || platform;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            refreshData();
            
            // 自动刷新
            setInterval(refreshData, 30000); // 30秒刷新一次
        });
    </script>
</body>
</html>
