# 宝塔面板文件上传指南

## 方法一：在线文件管理器上传（推荐）

### 1. 准备项目文件
首先将项目打包成ZIP文件：
```bash
# 在本地打包项目
zip -r unified_api.zip unified_api/
```

### 2. 上传文件
1. 登录宝塔面板
2. 点击 **文件** 菜单
3. 进入网站根目录：`/www/wwwroot/你的域名/`
4. 点击 **上传** 按钮
5. 选择 `unified_api.zip` 文件上传
6. 上传完成后，右键点击ZIP文件选择 **解压**
7. 将解压后的文件移动到网站根目录

### 3. 设置文件权限
在文件管理器中：
1. 选中整个项目目录
2. 右键选择 **权限**
3. 设置权限为 **755**
4. 勾选 **应用到子目录**

特别设置缓存和日志目录权限：
1. 进入 `cache/` 目录，设置权限为 **777**
2. 进入 `logs/` 目录，设置权限为 **777**

## 方法二：FTP上传

### 1. 开启FTP服务
1. 在宝塔面板点击 **FTP**
2. 添加FTP账户
3. 设置用户名、密码和根目录

### 2. 使用FTP客户端上传
推荐使用FileZilla：
1. 连接到FTP服务器
2. 上传项目文件到网站根目录
3. 确保文件完整性

## 方法三：Git克隆（推荐开发者）

### 1. 安装Git
在宝塔面板的 **软件商店** 中安装Git。

### 2. 克隆项目
在 **终端** 中执行：
```bash
cd /www/wwwroot/你的域名/
git clone https://github.com/your-repo/unified_api.git .
```

### 3. 设置权限
```bash
chown -R www:www /www/wwwroot/你的域名/
chmod -R 755 /www/wwwroot/你的域名/
chmod -R 777 /www/wwwroot/你的域名/cache
chmod -R 777 /www/wwwroot/你的域名/logs
```

## 文件结构检查

上传完成后，确保目录结构如下：
```
/www/wwwroot/你的域名/
├── index.php                 # 主入口文件
├── config/
│   └── config.php           # 配置文件
├── core/                    # 核心文件
├── utils/                   # 工具类
├── admin/                   # 管理面板
├── test/                    # 测试文件
├── cache/                   # 缓存目录（777权限）
├── logs/                    # 日志目录（777权限）
├── README.md
└── ...
```

## 配置修改

### 1. 修改配置文件
编辑 `config/config.php`：

```php
// 根据实际情况修改配置
define('CACHE_ENABLE', true);
define('CACHE_EXPIRE_TIME', 3600);
define('LOG_ENABLE', true);
define('LOG_LEVEL', 'INFO');

// 如果启用数据库
define('DB_ENABLE', true);
define('DB_HOST', 'localhost');
define('DB_NAME', '数据库名');
define('DB_USER', '数据库用户名');
define('DB_PASS', '数据库密码');
```

### 2. 创建数据库（如果需要）
1. 在宝塔面板点击 **数据库**
2. 点击 **添加数据库**
3. 填写数据库名、用户名、密码
4. 记录数据库信息，用于配置文件

### 3. 测试配置
访问：`http://你的域名/admin/status.php`
检查系统状态是否正常。

## 常见问题解决

### 1. 权限问题
如果出现权限错误：
```bash
# 重新设置权限
chown -R www:www /www/wwwroot/你的域名/
find /www/wwwroot/你的域名/ -type d -exec chmod 755 {} \;
find /www/wwwroot/你的域名/ -type f -exec chmod 644 {} \;
chmod -R 777 /www/wwwroot/你的域名/cache
chmod -R 777 /www/wwwroot/你的域名/logs
```

### 2. 缓存目录不可写
```bash
# 确保缓存目录存在且可写
mkdir -p /www/wwwroot/你的域名/cache
mkdir -p /www/wwwroot/你的域名/logs
chmod 777 /www/wwwroot/你的域名/cache
chmod 777 /www/wwwroot/你的域名/logs
```

### 3. PHP扩展缺失
在宝塔面板的PHP设置中安装缺失的扩展：
- curl
- json
- mbstring
- openssl

### 4. 内存限制
在PHP配置中增加内存限制：
```ini
memory_limit = 512M
```

### 5. 执行时间限制
```ini
max_execution_time = 60
```

## 安全建议

### 1. 隐藏敏感目录
在网站设置的 **伪静态** 中添加：
```nginx
location ~ ^/(config|logs|cache)/ {
    deny all;
}
```

### 2. 设置访问限制
可以在 **访问限制** 中设置IP白名单。

### 3. 开启防火墙
确保只开放必要的端口（80, 443）。

## 性能优化

### 1. 开启OPcache
在PHP设置中开启OPcache扩展。

### 2. 配置Redis缓存
如果安装了Redis，在配置文件中启用：
```php
define('CACHE_STRATEGY', 'redis');
define('REDIS_HOST', '127.0.0.1');
define('REDIS_PORT', 6379);
```

### 3. 开启Gzip压缩
在网站设置中开启Gzip压缩。

## 监控和维护

### 1. 设置定时任务
在 **计划任务** 中添加：
```bash
# 每小时执行维护任务
0 * * * * cd /www/wwwroot/你的域名 && php cron/maintenance.php
```

### 2. 监控日志
定期查看：
- Nginx访问日志
- PHP错误日志
- 应用日志

### 3. 备份设置
配置自动备份：
- 网站文件备份
- 数据库备份
- 配置文件备份
