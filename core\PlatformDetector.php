<?php
/**
 * 平台检测器
 * 根据URL自动检测所属平台
 */

require_once __DIR__ . '/../utils/HttpClient.php';

class PlatformDetector
{
    private $supportedPlatforms;
    private $httpClient;

    public function __construct()
    {
        $this->supportedPlatforms = getSupportedPlatforms();
        $this->httpClient = new HttpClient();
    }

    /**
     * 检测URL所属平台
     */
    public function detect($url)
    {
        try {
            // 清理URL
            $cleanUrl = $this->httpClient->cleanUrl($url);
            
            // 解析URL
            $parsed = parse_url($cleanUrl);
            if (!$parsed || !isset($parsed['host'])) {
                return null;
            }

            $host = strtolower($parsed['host']);
            
            // 移除www前缀
            $host = preg_replace('/^www\./', '', $host);

            // 直接域名匹配
            $platform = $this->matchByDomain($host);
            if ($platform) {
                return $platform;
            }

            // 处理短链接，获取重定向后的URL
            if ($this->isShortUrl($host)) {
                try {
                    $redirectUrl = $this->httpClient->getRedirectUrl($url);
                    if ($redirectUrl && $redirectUrl !== $url) {
                        return $this->detect($redirectUrl);
                    }
                } catch (Exception $e) {
                    // 重定向失败，继续尝试其他方法
                }
            }

            // 特殊处理某些平台的分享链接
            return $this->detectSpecialCases($url, $host, $parsed);

        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 通过域名匹配平台
     */
    private function matchByDomain($host)
    {
        foreach ($this->supportedPlatforms as $platform => $config) {
            foreach ($config['domains'] as $domain) {
                if (strpos($host, $domain) !== false) {
                    return $platform;
                }
            }
        }
        return null;
    }

    /**
     * 检查是否为短链接
     */
    private function isShortUrl($host)
    {
        $shortDomains = [
            'b23.tv',           // B站短链接
            'v.douyin.com',     // 抖音短链接
            'xhslink.com',      // 小红书短链接
            't.cn',             // 微博短链接
            'dwz.cn',           // 通用短链接
            'suo.im',           // 通用短链接
            'bit.ly',           // 国外短链接
            'tinyurl.com',      // 国外短链接
        ];

        foreach ($shortDomains as $shortDomain) {
            if (strpos($host, $shortDomain) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 处理特殊情况
     */
    private function detectSpecialCases($url, $host, $parsed)
    {
        // 抖音特殊处理
        if (preg_match('/douyin|dy|iesdouyin/', $host)) {
            return 'douyin';
        }

        // 快手特殊处理
        if (preg_match('/kuaishou|ks|kwai|chenzhongtech|v\.kuaishou/', $host)) {
            return 'kuaishou';
        }

        // 小红书特殊处理
        if (preg_match('/xiaohongshu|xhs|redbook/', $host)) {
            return 'xiaohongshu';
        }

        // B站特殊处理
        if (preg_match('/bilibili|b23|acg/', $host)) {
            return 'bilibili';
        }

        // 微博特殊处理
        if (preg_match('/weibo|sina/', $host)) {
            return 'weibo';
        }

        // 皮皮搞笑特殊处理
        if (preg_match('/pipigx|ppgx/', $host)) {
            return 'pipigx';
        }

        // 皮皮虾特殊处理
        if (preg_match('/ppxia|ppx/', $host)) {
            return 'ppxia';
        }

        // 通过URL路径特征检测
        if (isset($parsed['path'])) {
            $path = $parsed['path'];
            
            // 抖音视频路径特征
            if (preg_match('/\/video\/\d+/', $path)) {
                return 'douyin';
            }
            
            // B站视频路径特征
            if (preg_match('/\/video\/[A-Za-z0-9]+/', $path)) {
                return 'bilibili';
            }
            
            // 小红书笔记路径特征
            if (preg_match('/\/discovery\/item\/[a-f0-9]+/', $path)) {
                return 'xiaohongshu';
            }
        }

        return null;
    }

    /**
     * 获取支持的平台列表
     */
    public function getSupportedPlatforms()
    {
        return array_keys($this->supportedPlatforms);
    }

    /**
     * 获取平台信息
     */
    public function getPlatformInfo($platform)
    {
        return $this->supportedPlatforms[$platform] ?? null;
    }

    /**
     * 检查平台是否支持
     */
    public function isSupported($platform)
    {
        return isset($this->supportedPlatforms[$platform]);
    }
}
?>
