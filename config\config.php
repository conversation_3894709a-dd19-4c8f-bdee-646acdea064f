<?php
/**
 * 配置文件
 */

// 基础配置
define('API_VERSION', '1.0.0');
define('API_NAME', '短视频去水印统一解析接口');

// 缓存配置
define('CACHE_ENABLE', true);
define('CACHE_EXPIRE_TIME', 3600); // 1小时
define('CACHE_DIR', __DIR__ . '/../cache/');

// 日志配置
define('LOG_ENABLE', true);
define('LOG_DIR', __DIR__ . '/../logs/');
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARN, ERROR

// 数据库配置
define('DB_ENABLE', false); // 是否启用数据库功能
define('DB_HOST', 'localhost');
define('DB_NAME', 'video_parser');
define('DB_USER', 'root');
define('DB_PASS', '');

// 认证配置
define('AUTH_ENABLE', false); // 是否启用API认证
define('BATCH_ENABLE', false); // 是否启用批量处理

// 请求配置
define('REQUEST_TIMEOUT', 30); // 请求超时时间（秒）
define('MAX_REDIRECTS', 5); // 最大重定向次数

// 支持的平台配置
$SUPPORTED_PLATFORMS = [
    'douyin' => [
        'name' => '抖音',
        'domains' => ['douyin.com', 'iesdouyin.com', 'v.douyin.com'],
        'class' => 'DouyinParser'
    ],
    'kuaishou' => [
        'name' => '快手',
        'domains' => ['kuaishou.com', 'chenzhongtech.com', 'kwai.com'],
        'class' => 'KuaishouParser'
    ],
    'xiaohongshu' => [
        'name' => '小红书',
        'domains' => ['xiaohongshu.com', 'xhslink.com', 'xhs.com'],
        'class' => 'XiaohongshuParser'
    ],
    'bilibili' => [
        'name' => '哔哩哔哩',
        'domains' => ['bilibili.com', 'b23.tv'],
        'class' => 'BilibiliParser'
    ],
    'weibo' => [
        'name' => '微博',
        'domains' => ['weibo.com', 'weibo.cn'],
        'class' => 'WeiboParser'
    ],
    'pipigx' => [
        'name' => '皮皮搞笑',
        'domains' => ['pipigx.com'],
        'class' => 'PipigxParser'
    ],
    'ppxia' => [
        'name' => '皮皮虾',
        'domains' => ['ppxia.com'],
        'class' => 'PpxiaParser'
    ],
    'qishui_music' => [
        'name' => '汽水音乐',
        'domains' => ['music.douyin.com', 'qishui.douyin.com'],
        'class' => 'QishuiMusicParser'
    ],
    'netease_music' => [
        'name' => '网易云音乐',
        'domains' => ['music.163.com', '163cn.tv'],
        'class' => 'NeteaseMusicParser'
    ]
];

// User-Agent配置
$USER_AGENTS = [
    'mobile' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
    'desktop' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'android' => 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36'
];

// 错误码配置
$ERROR_CODES = [
    200 => '解析成功',
    400 => '请求参数错误',
    404 => '解析失败',
    429 => '请求过于频繁',
    500 => '服务器内部错误',
    503 => '服务暂不可用'
];

// 创建必要的目录
if (!is_dir(CACHE_DIR)) {
    mkdir(CACHE_DIR, 0755, true);
}

if (!is_dir(LOG_DIR)) {
    mkdir(LOG_DIR, 0755, true);
}

// 全局函数
function getSupportedPlatforms()
{
    global $SUPPORTED_PLATFORMS;
    return $SUPPORTED_PLATFORMS;
}

function getUserAgents()
{
    global $USER_AGENTS;
    return $USER_AGENTS;
}

function getErrorCodes()
{
    global $ERROR_CODES;
    return $ERROR_CODES;
}
?>
