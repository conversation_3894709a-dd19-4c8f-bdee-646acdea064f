<?php
/**
 * 批量处理器
 */

class BatchProcessor
{
    private $api;
    private $db;
    private $maxBatchSize = 50;
    private $maxConcurrency = 5;

    public function __construct($api, $db = null)
    {
        $this->api = $api;
        $this->db = $db;
        $this->initQueueTables();
    }

    /**
     * 初始化队列表
     */
    private function initQueueTables()
    {
        if (!$this->db) return;

        $sql = "
            CREATE TABLE IF NOT EXISTS batch_jobs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                job_id VARCHAR(32) NOT NULL UNIQUE,
                urls JSON NOT NULL,
                status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
                total_urls INT NOT NULL,
                processed_urls INT DEFAULT 0,
                success_count INT DEFAULT 0,
                failed_count INT DEFAULT 0,
                results JSON,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                started_at TIMESTAMP NULL,
                completed_at TIMESTAMP NULL,
                INDEX idx_status (status),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";

        try {
            $this->db->pdo->exec($sql);
        } catch (PDOException $e) {
            // 表可能已存在
        }
    }

    /**
     * 创建批量任务
     */
    public function createBatchJob($urls)
    {
        if (count($urls) > $this->maxBatchSize) {
            throw new Exception("批量大小超过限制 ({$this->maxBatchSize})");
        }

        $jobId = $this->generateJobId();
        
        if ($this->db) {
            $sql = "INSERT INTO batch_jobs (job_id, urls, total_urls) VALUES (?, ?, ?)";
            $stmt = $this->db->pdo->prepare($sql);
            $stmt->execute([$jobId, json_encode($urls), count($urls)]);
        }

        // 异步处理（如果支持）
        if (function_exists('fastcgi_finish_request')) {
            fastcgi_finish_request();
            $this->processBatchJob($jobId, $urls);
        }

        return [
            'job_id' => $jobId,
            'total_urls' => count($urls),
            'status' => 'pending',
            'check_url' => "/batch/status/{$jobId}"
        ];
    }

    /**
     * 处理批量任务
     */
    public function processBatchJob($jobId, $urls = null)
    {
        if (!$urls && $this->db) {
            $job = $this->getBatchJob($jobId);
            if (!$job) {
                throw new Exception("任务不存在: {$jobId}");
            }
            $urls = json_decode($job['urls'], true);
        }

        $this->updateJobStatus($jobId, 'processing');
        
        $results = [];
        $successCount = 0;
        $failedCount = 0;

        try {
            // 分块处理URL
            $chunks = array_chunk($urls, $this->maxConcurrency);
            
            foreach ($chunks as $chunk) {
                $chunkResults = $this->processUrlChunk($chunk);
                
                foreach ($chunkResults as $result) {
                    $results[] = $result;
                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $failedCount++;
                    }
                }

                // 更新进度
                $this->updateJobProgress($jobId, count($results), $successCount, $failedCount);
                
                // 避免过载
                usleep(100000); // 0.1秒延迟
            }

            $this->completeJob($jobId, $results, $successCount, $failedCount);
            
        } catch (Exception $e) {
            $this->failJob($jobId, $e->getMessage());
            throw $e;
        }

        return $results;
    }

    /**
     * 处理URL块
     */
    private function processUrlChunk($urls)
    {
        $results = [];
        
        // 使用多进程或多线程（如果支持）
        if (function_exists('pcntl_fork') && count($urls) > 1) {
            $results = $this->processUrlsParallel($urls);
        } else {
            $results = $this->processUrlsSequential($urls);
        }
        
        return $results;
    }

    /**
     * 顺序处理URLs
     */
    private function processUrlsSequential($urls)
    {
        $results = [];
        
        foreach ($urls as $url) {
            $startTime = microtime(true);
            
            try {
                $result = $this->api->handleSingleUrl($url);
                $results[] = [
                    'url' => $url,
                    'success' => true,
                    'data' => $result,
                    'processing_time' => round((microtime(true) - $startTime) * 1000, 2)
                ];
            } catch (Exception $e) {
                $results[] = [
                    'url' => $url,
                    'success' => false,
                    'error' => $e->getMessage(),
                    'processing_time' => round((microtime(true) - $startTime) * 1000, 2)
                ];
            }
        }
        
        return $results;
    }

    /**
     * 并行处理URLs（需要pcntl扩展）
     */
    private function processUrlsParallel($urls)
    {
        $results = [];
        $children = [];
        
        foreach ($urls as $index => $url) {
            $pid = pcntl_fork();
            
            if ($pid == -1) {
                // Fork失败，回退到顺序处理
                return $this->processUrlsSequential($urls);
            } elseif ($pid == 0) {
                // 子进程
                $startTime = microtime(true);
                
                try {
                    $result = $this->api->handleSingleUrl($url);
                    $output = [
                        'url' => $url,
                        'success' => true,
                        'data' => $result,
                        'processing_time' => round((microtime(true) - $startTime) * 1000, 2)
                    ];
                } catch (Exception $e) {
                    $output = [
                        'url' => $url,
                        'success' => false,
                        'error' => $e->getMessage(),
                        'processing_time' => round((microtime(true) - $startTime) * 1000, 2)
                    ];
                }
                
                // 将结果写入临时文件
                file_put_contents("/tmp/batch_result_{$index}.json", json_encode($output));
                exit(0);
            } else {
                // 父进程
                $children[$index] = $pid;
            }
        }
        
        // 等待所有子进程完成
        foreach ($children as $index => $pid) {
            pcntl_waitpid($pid, $status);
            
            // 读取结果
            $resultFile = "/tmp/batch_result_{$index}.json";
            if (file_exists($resultFile)) {
                $result = json_decode(file_get_contents($resultFile), true);
                $results[] = $result;
                unlink($resultFile);
            }
        }
        
        return $results;
    }

    /**
     * 获取批量任务状态
     */
    public function getBatchJobStatus($jobId)
    {
        if (!$this->db) {
            return ['error' => '数据库未配置'];
        }

        $job = $this->getBatchJob($jobId);
        if (!$job) {
            return ['error' => '任务不存在'];
        }

        $response = [
            'job_id' => $job['job_id'],
            'status' => $job['status'],
            'total_urls' => $job['total_urls'],
            'processed_urls' => $job['processed_urls'],
            'success_count' => $job['success_count'],
            'failed_count' => $job['failed_count'],
            'created_at' => $job['created_at'],
            'started_at' => $job['started_at'],
            'completed_at' => $job['completed_at']
        ];

        if ($job['status'] === 'completed' && $job['results']) {
            $response['results'] = json_decode($job['results'], true);
        }

        if ($job['status'] === 'failed') {
            $response['error_message'] = $job['error_message'];
        }

        // 计算进度百分比
        if ($job['total_urls'] > 0) {
            $response['progress_percent'] = round(($job['processed_urls'] / $job['total_urls']) * 100, 2);
        }

        return $response;
    }

    /**
     * 获取批量任务
     */
    private function getBatchJob($jobId)
    {
        $sql = "SELECT * FROM batch_jobs WHERE job_id = ?";
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute([$jobId]);
        return $stmt->fetch();
    }

    /**
     * 更新任务状态
     */
    private function updateJobStatus($jobId, $status)
    {
        if (!$this->db) return;

        $sql = "UPDATE batch_jobs SET status = ?";
        $params = [$status];

        if ($status === 'processing') {
            $sql .= ", started_at = NOW()";
        } elseif ($status === 'completed' || $status === 'failed') {
            $sql .= ", completed_at = NOW()";
        }

        $sql .= " WHERE job_id = ?";
        $params[] = $jobId;

        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute($params);
    }

    /**
     * 更新任务进度
     */
    private function updateJobProgress($jobId, $processed, $success, $failed)
    {
        if (!$this->db) return;

        $sql = "UPDATE batch_jobs SET processed_urls = ?, success_count = ?, failed_count = ? WHERE job_id = ?";
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute([$processed, $success, $failed, $jobId]);
    }

    /**
     * 完成任务
     */
    private function completeJob($jobId, $results, $successCount, $failedCount)
    {
        if (!$this->db) return;

        $sql = "UPDATE batch_jobs SET 
                status = 'completed', 
                results = ?, 
                success_count = ?, 
                failed_count = ?,
                completed_at = NOW() 
                WHERE job_id = ?";
        
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute([json_encode($results), $successCount, $failedCount, $jobId]);
    }

    /**
     * 任务失败
     */
    private function failJob($jobId, $errorMessage)
    {
        if (!$this->db) return;

        $sql = "UPDATE batch_jobs SET status = 'failed', error_message = ?, completed_at = NOW() WHERE job_id = ?";
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute([$errorMessage, $jobId]);
    }

    /**
     * 生成任务ID
     */
    private function generateJobId()
    {
        return uniqid('batch_', true);
    }

    /**
     * 清理过期任务
     */
    public function cleanupOldJobs($days = 7)
    {
        if (!$this->db) return 0;

        $sql = "DELETE FROM batch_jobs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute([$days]);
        
        return $stmt->rowCount();
    }
}
?>
