<?php
/**
 * 响应格式化器
 * 统一API响应格式
 */

class ResponseFormatter
{
    /**
     * 成功响应
     */
    public function success($data, $message = '解析成功')
    {
        return json_encode([
            'code' => 200,
            'msg' => $message,
            'data' => $data,
            'timestamp' => time(),
            'api_version' => API_VERSION
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * 错误响应
     */
    public function error($code, $message, $data = null)
    {
        $response = [
            'code' => $code,
            'msg' => $message,
            'timestamp' => time(),
            'api_version' => API_VERSION
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * 分页响应
     */
    public function paginated($data, $total, $page, $pageSize, $message = '获取成功')
    {
        return json_encode([
            'code' => 200,
            'msg' => $message,
            'data' => $data,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize,
                'total_pages' => ceil($total / $pageSize)
            ],
            'timestamp' => time(),
            'api_version' => API_VERSION
        ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }

    /**
     * 获取错误消息
     */
    public function getErrorMessage($code)
    {
        $errorCodes = getErrorCodes();
        return $errorCodes[$code] ?? '未知错误';
    }
}
?>
