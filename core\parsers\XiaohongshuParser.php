<?php
/**
 * 小红书解析器
 */

class XiaohongshuParser extends AbstractParser
{
    /**
     * 解析小红书内容
     */
    public function parse($url)
    {
        try {
            $this->info("开始解析小红书URL: $url");
            
            // 处理特殊的xhs.com域名
            $processedUrl = $this->processUrl($url);
            $this->debug("处理后的URL: $processedUrl");

            // 获取内容信息
            $contentInfo = $this->getContentInfo($processedUrl);
            if (!$contentInfo) {
                return $this->handleError('获取内容信息失败');
            }

            // 格式化数据
            $formattedData = $this->formatData($contentInfo);
            $formattedData['source_url'] = $url;

            $this->info("小红书内容解析成功");
            return $formattedData;

        } catch (Exception $e) {
            return $this->handleError('解析过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * 处理URL
     */
    private function processUrl($url)
    {
        $domain = parse_url($url, PHP_URL_HOST);
        
        if ($domain === 'xhs.com') {
            // 提取ID并转换为xhslink.com格式
            $parts = explode('/', $url);
            if (count($parts) >= 5) {
                $id = $parts[4];
                return "http://xhslink.com/a/$id";
            }
        }
        
        return $url;
    }

    /**
     * 提取内容ID（小红书不需要单独提取ID）
     */
    protected function extractVideoId($url)
    {
        // 小红书的ID提取在processUrl中处理
        return true;
    }

    /**
     * 获取内容信息
     */
    protected function getVideoInfo($url)
    {
        return $this->getContentInfo($url);
    }

    /**
     * 获取内容信息（完全基于原项目xhsjx.php逻辑）
     */
    private function getContentInfo($url)
    {
        try {
            $this->simpleLog("getContentInfo开始: $url");
            // 处理URL格式（基于原项目逻辑）
            $domain = parse_url($url);
            if ($domain['host'] == "www.xiaohongshu.com") {
                $id = $this->extractContentId($url);
                $finalUrl = $url;
            } else {
                // 获取重定向
                $redirectHeaders = get_headers($url, 1);
                $location = $redirectHeaders["Location"] ?? $url;
                if (is_array($location)) {
                    $location = $location[0];
                }
                $finalUrl = $location;
                $id = $this->extractContentId($finalUrl);
            }

            $this->debug("URL处理完成", ['finalUrl' => $finalUrl, 'id' => $id]);

            if (!$id) {
                $this->debug("无法提取内容ID");
                return null;
            }

            // 使用原项目的请求方式
            $cookie = "xhsTrackerId=e6018ab9-6936-4b02-cb65-a7f9f9e22ea0; xhsuid=y2PCwPFU9GCQnJH8; timestamp2=20210607d2293bcc8dcad65834920376; timestamp2.sig=QFn2Zv9pjUr07KDlnh886Yq43bZxOaT6t3WCzZdzcgM; xhsTracker=url=noteDetail&xhsshare=CopyLink; extra_exp_ids=gif_exp1,ques_exp2";

            $response = $this->getCurl($finalUrl, $cookie);
            if (!$response) {
                $this->debug("HTTP请求失败");
                return null;
            }

            $this->debug("HTTP请求成功", ['responseLength' => strlen($response)]);

            // 使用原项目的正则表达式
            $pattern = '/<script>\s*window.__INITIAL_STATE__\s*=\s*({[\s\S]*?})<\/script>/is';
            if (!preg_match($pattern, $response, $matches)) {
                $this->debug("未找到__INITIAL_STATE__数据");
                return null;
            }

            $this->debug("找到__INITIAL_STATE__数据");
            $jsonData = $matches[1];
            // 将 undefined 替换为 null
            $jsonData = str_replace('undefined', 'null', $jsonData);

            $decoded = json_decode($jsonData, true);
            if (!$decoded) {
                $this->debug("JSON解析失败");
                return null;
            }

            $this->debug("JSON解析成功，开始数据提取");

            // 基于原项目逻辑：使用safeGet方法安全获取数据

            // 完全按照原项目逻辑进行数据提取

            // 安全获取视频URL（按原项目的优先级）
            $videoH265Url = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'video', 'media', 'stream', 'h265', 0, 'masterUrl']);
            $videoH264Url = $this->safeGet($decoded, ['note', 'noteDetailMap', $id, 'note', 'video', 'media', 'stream', 'h264', 0, 'backupUrls', 0]);
            $videourl = $videoH265Url ?: $videoH264Url; // 使用原项目的变量名

            $this->simpleLog("视频URL检查: h265=" . ($videoH265Url ? '找到' : '未找到') . ", h264=" . ($videoH264Url ? '找到' : '未找到') . ", final=" . ($videourl ? '有视频' : '无视频'));

            if ($videourl) {
                $this->simpleLog("找到视频URL: $videourl");
            } else {
                $this->simpleLog("未找到视频URL，将解析为图集");
            }

            // 获取图片数据（作为备用数据源）
            $imageData = $this->safeGet($decoded, ['note', 'noteDetailMap', $id, 'note']);

            $this->debug("图片数据检查", ['imageData' => $imageData ? '找到' : '未找到']);

            // 获取作者信息（多路径获取）
            $author = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'user', 'nickName']);
            $author = $author ?: $this->safeGet($imageData, ['user', 'nickname'], '');

            $authorID = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'user', 'userId']);
            $authorID = $authorID ?: $this->safeGet($imageData, ['user', 'userId'], '');

            // 获取标题和描述
            $title = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'title']);
            $title = $title ?: $this->safeGet($imageData, ['title'], '');

            $desc = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'desc']);
            $desc = $desc ?: $this->safeGet($imageData, ['desc']);
            $desc = $desc ?: $this->safeGet($decoded, ['note', 'noteDetailMap', $id, 'note'], '');

            // 获取头像和封面
            $avatar = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'user', 'avatar']);
            $avatar = $avatar ?: $this->safeGet($imageData, ['user', 'avatar'], '');

            $cover = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'imageList', 0, 'url']);
            $cover = $cover ?: $this->safeGet($decoded, ['note', 'noteDetailMap', $id, 'note', 'imageList', 0, 'urlDefault'], '');

            $this->debug("基础信息提取完成", [
                'author' => $author ? '有' : '无',
                'title' => $title ? '有' : '无',
                'cover' => $cover ? '有' : '无'
            ]);

            // 处理图片（多路径获取）
            $images = [];

            // 路径1：从noteData获取
            $imageList1 = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'imageList'], []);
            foreach ($imageList1 as $image) {
                if (isset($image['urlDefault'])) {
                    $images[] = $image['urlDefault'];
                } elseif (isset($image['url'])) {
                    $images[] = $image['url'];
                }
            }

            // 路径2：从note.noteDetailMap获取（如果第一种方式没有图片）
            if (empty($images) && $imageData) {
                $imageList2 = $this->safeGet($imageData, ['imageList'], []);
                foreach ($imageList2 as $image) {
                    if (isset($image['urlDefault'])) {
                        $images[] = $image['urlDefault'];
                    } elseif (isset($image['url'])) {
                        $images[] = $image['url'];
                    }
                }
            }

            // 获取统计信息
            $likeCount = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'interactInfo', 'likedCount'], 0);
            $likeCount = $likeCount ?: $this->safeGet($imageData, ['interactInfo', 'likedCount'], 0);

            $commentCount = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'interactInfo', 'commentCount'], 0);
            $commentCount = $commentCount ?: $this->safeGet($imageData, ['interactInfo', 'commentCount'], 0);

            $shareCount = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'interactInfo', 'shareCount'], 0);
            $shareCount = $shareCount ?: $this->safeGet($imageData, ['interactInfo', 'shareCount'], 0);

            $viewCount = $this->safeGet($decoded, ['noteData', 'data', 'noteData', 'interactInfo', 'viewCount'], 0);
            $viewCount = $viewCount ?: $this->safeGet($imageData, ['interactInfo', 'viewCount'], 0);

            // 完全按照原项目逻辑构造数据
            if (!empty($videourl)) {
                // 有视频URL，返回视频数据
                $this->debug("检测到视频内容", ['videoUrl' => $videourl]);

                $data = [
                    'title' => $title,
                    'desc' => $desc,
                    'author' => $author,
                    'author_id' => $authorID,
                    'avatar' => $avatar,
                    'cover' => $cover,
                    'url' => $videourl, // 视频URL
                    'like' => $likeCount,
                    'comment' => $commentCount,
                    'share' => $shareCount,
                    'view' => $viewCount,
                    'time' => time(),
                    'type' => 'video' // 明确标记为视频
                ];
            } elseif (!empty($images)) {
                // 没有视频URL但有图片，返回图集数据
                $this->debug("检测到图集内容", ['imageCount' => count($images)]);

                $data = [
                    'title' => $title,
                    'desc' => $desc,
                    'author' => $author,
                    'author_id' => $authorID,
                    'avatar' => $avatar,
                    'cover' => $cover ?: ($images[0] ?? ''),
                    'images' => $images, // 图片数组
                    'like' => $likeCount,
                    'comment' => $commentCount,
                    'share' => $shareCount,
                    'view' => $viewCount,
                    'time' => time(),
                    'type' => 'image' // 明确标记为图集
                ];
            } else {
                $this->debug("既没有视频也没有图片");
                return null;
            }

            $this->debug("数据构造完成", [
                'type' => $data['type'],
                'hasTitle' => !empty($data['title']),
                'hasAuthor' => !empty($data['author']),
                'hasVideoUrl' => !empty($data['url']),
                'imageCount' => count($data['images'] ?? [])
            ]);

            return $data;

        } catch (Exception $e) {
            $this->debug("获取小红书内容信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取重定向URL
     */
    private function getRedirectUrl($url)
    {
        $domain = parse_url($url, PHP_URL_HOST);

        if ($domain !== 'www.xiaohongshu.com') {
            // 获取重定向
            $headers = get_headers($url, true, stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]));
            return $headers['Location'] ?? $url;
        }

        return $url;
    }

    /**
     * 提取内容ID
     */
    private function extractContentId($url)
    {
        $patterns = [
            '/discovery\/item\/([a-zA-Z0-9]+)/',
            '/explore\/([a-zA-Z0-9]+)/',
            '/\/([a-zA-Z0-9]+)\?/',
            '/\/([a-zA-Z0-9]+)$/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * 原项目的getCurl方法
     */
    private function getCurl($url, $cookie = null)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1');

        if ($cookie) {
            curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode >= 200 && $httpCode < 300) {
            return $response;
        }

        return false;
    }

    /**
     * 安全获取嵌套数组的值（基于原项目逻辑）
     */
    private function safeGet($data, $path, $default = null)
    {
        $current = $data;

        foreach ($path as $key) {
            if (is_array($current) && isset($current[$key])) {
                $current = $current[$key];
            } else {
                return $default;
            }
        }

        return $current;
    }

    /**
     * 获取嵌套数组的值（保留兼容性）
     */
    private function getNestedValue($array, $path)
    {
        $keys = explode('.', $path);
        return $this->safeGet($array, $keys);
    }

    /**
     * 简单日志记录
     */
    private function simpleLog($message)
    {
        $logFile = __DIR__ . '/../../logs/debug.log';
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [xiaohongshu] $message\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}
?>
