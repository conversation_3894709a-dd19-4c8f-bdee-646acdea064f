<?php
/**
 * 系统状态监控页面
 */

require_once '../config/config.php';
require_once '../core/CacheManager.php';
require_once '../utils/Logger.php';
require_once '../utils/RateLimiter.php';

header('Content-Type: application/json; charset=utf-8');

class SystemStatus
{
    private $cacheManager;
    private $logger;
    private $rateLimiter;

    public function __construct()
    {
        $this->cacheManager = new CacheManager();
        $this->logger = new Logger();
        $this->rateLimiter = new RateLimiter();
    }

    /**
     * 获取系统状态
     */
    public function getStatus()
    {
        return [
            'api_info' => $this->getApiInfo(),
            'system_info' => $this->getSystemInfo(),
            'cache_stats' => $this->getCacheStats(),
            'supported_platforms' => $this->getSupportedPlatforms(),
            'recent_logs' => $this->getRecentLogs(),
            'performance' => $this->getPerformanceStats()
        ];
    }

    /**
     * 获取API信息
     */
    private function getApiInfo()
    {
        return [
            'name' => API_NAME,
            'version' => API_VERSION,
            'uptime' => $this->getUptime(),
            'timestamp' => time(),
            'timezone' => date_default_timezone_get()
        ];
    }

    /**
     * 获取系统信息
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit')
            ],
            'disk_space' => [
                'free' => disk_free_space('.'),
                'total' => disk_total_space('.')
            ],
            'extensions' => [
                'curl' => extension_loaded('curl'),
                'json' => extension_loaded('json'),
                'mbstring' => extension_loaded('mbstring')
            ]
        ];
    }

    /**
     * 获取缓存统计
     */
    private function getCacheStats()
    {
        if (!CACHE_ENABLE) {
            return ['enabled' => false];
        }

        $stats = $this->cacheManager->getStats();
        return array_merge(['enabled' => true], $stats ?: []);
    }

    /**
     * 获取支持的平台
     */
    private function getSupportedPlatforms()
    {
        $platforms = getSupportedPlatforms();
        $result = [];
        
        foreach ($platforms as $key => $config) {
            $result[$key] = [
                'name' => $config['name'],
                'domains' => $config['domains'],
                'parser_exists' => file_exists(__DIR__ . "/../core/parsers/{$config['class']}.php")
            ];
        }
        
        return $result;
    }

    /**
     * 获取最近的日志
     */
    private function getRecentLogs($lines = 50)
    {
        if (!LOG_ENABLE) {
            return ['enabled' => false];
        }

        $logFile = LOG_DIR . date('Y-m-d') . '.log';
        if (!file_exists($logFile)) {
            return ['enabled' => true, 'logs' => []];
        }

        $logs = [];
        $file = new SplFileObject($logFile);
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();
        
        $startLine = max(0, $totalLines - $lines);
        $file->seek($startLine);
        
        while (!$file->eof()) {
            $line = trim($file->current());
            if (!empty($line)) {
                $logs[] = $line;
            }
            $file->next();
        }

        return [
            'enabled' => true,
            'total_lines' => $totalLines,
            'showing_lines' => count($logs),
            'logs' => $logs
        ];
    }

    /**
     * 获取性能统计
     */
    private function getPerformanceStats()
    {
        return [
            'request_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT'],
            'memory_usage' => memory_get_usage(true),
            'included_files' => count(get_included_files())
        ];
    }

    /**
     * 获取运行时间
     */
    private function getUptime()
    {
        // 简单的运行时间计算，基于日志文件的创建时间
        $logFile = LOG_DIR . date('Y-m-d') . '.log';
        if (file_exists($logFile)) {
            return time() - filemtime($logFile);
        }
        return 0;
    }

    /**
     * 清理系统
     */
    public function cleanup()
    {
        $results = [];
        
        // 清理过期缓存
        if (CACHE_ENABLE) {
            $results['cache_cleaned'] = $this->cacheManager->cleanExpired();
        }
        
        // 清理过期日志
        if (LOG_ENABLE) {
            $this->logger->cleanOldLogs(30);
            $results['logs_cleaned'] = true;
        }
        
        // 清理速率限制文件
        $results['rate_limit_cleaned'] = $this->rateLimiter->cleanup();
        
        return $results;
    }
}

// 处理请求
$action = $_GET['action'] ?? 'status';
$systemStatus = new SystemStatus();

switch ($action) {
    case 'status':
        echo json_encode($systemStatus->getStatus(), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        break;
        
    case 'cleanup':
        echo json_encode($systemStatus->cleanup(), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        break;
        
    default:
        http_response_code(400);
        echo json_encode(['error' => 'Invalid action'], JSON_UNESCAPED_UNICODE);
}
?>
