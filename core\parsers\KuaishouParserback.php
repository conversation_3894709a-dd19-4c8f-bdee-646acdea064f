<?php
/**
 * 快手解析器备用版本 - 基于原项目逻辑
 */

class KuaishouParserBackup extends AbstractParser
{
    /**
     * 解析快手内容（基于原项目逻辑）
     */
    public function parse($url)
    {
        try {
            $this->info("开始解析快手URL (备用版本): $url");
            
            // 获取重定向URL
            $finalUrl = $this->getRedirectUrl($url);
            $this->debug("重定向后的URL: $finalUrl");

            // 判断是视频还是图集
            if (strpos($finalUrl, '/photo/') !== false || strpos($finalUrl, '/fw/photo/') !== false) {
                // 图集解析
                return $this->parseAsImageOriginal($url, $finalUrl);
            } else {
                // 视频解析
                return $this->parseAsVideoOriginal($url, $finalUrl);
            }

        } catch (Exception $e) {
            return $this->handleError('解析过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * 按原项目逻辑解析图集
     */
    private function parseAsImageOriginal($originalUrl, $finalUrl)
    {
        try {
            $this->debug("使用原项目逻辑解析图集");
            
            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
            ];

            // 获取重定向后的location
            $redirectHeaders = get_headers($originalUrl, true, stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]));
            $location = $redirectHeaders['Location'] ?? $finalUrl;

            $response = $this->httpClient->get($location, $headers, 'desktop');
            if (!$response) {
                $this->debug("HTTP请求失败");
                return null;
            }

            $this->debug("HTTP请求成功，响应长度: " . strlen($response));

            // 使用原项目的正则模式
            $apolloStatePattern = '/window\.INIT_STATE\s*=\s*(.*?)\<\/script>/s';
            if (!preg_match($apolloStatePattern, $response, $matches)) {
                $this->debug("未找到INIT_STATE数据");
                return null;
            }

            $this->debug("找到INIT_STATE数据");
            $jsonString = stripslashes($matches[1]);
            
            // 第一次尝试解析
            $data = json_decode($jsonString, true);
            if (json_last_error() != JSON_ERROR_NONE) {
                $this->debug("第一次JSON解析失败，使用cleanInvalidJsonEscapes");
                $data = json_decode($this->cleanInvalidJsonEscapes($jsonString), true);
            }

            if (json_last_error() != JSON_ERROR_NONE) {
                $this->debug("JSON解析完全失败: " . json_last_error_msg());
                return null;
            }

            $this->debug("JSON解析成功");

            // 确保 $data 是数组或对象
            if (!is_array($data) && !is_object($data)) {
                $this->debug("数据类型非数组");
                return null;
            }

            // 查找包含fid的数据
            $filteredData = [];
            foreach ($data as $key => $value) {
                if (strpos($key, 'tusjoh') === 0 && isset($value['fid'])) {
                    $filteredData[$key] = $value;
                }
            }

            if (empty($filteredData)) {
                $this->debug("未找到包含fid的数据");
                return null;
            }

            $this->debug("找到包含fid的数据: " . count($filteredData) . " 个");

            // 获取第一个标签的值
            $firstValue = reset($filteredData);
            $imgjson = $firstValue;
            
            // 处理图片URL
            $img = $imgjson['photo']['ext_params']['atlas']['list'] ?? $imgjson['photo']['coverUrls'][0]['url'] ?? null;
            $music = 'http://txmov2.a.kwimgs.com' . ($imgjson['photo']['ext_params']['atlas']['music'] ?? $imgjson['photo']['music']['audioUrls'][0]['url'] ?? '');
            
            $images = [];
            if (is_array($img)) {
                foreach ($img as $imgItem) {
                    $images[] = 'http://tx2.a.yximgs.com/' . $imgItem;
                }
            } elseif (is_string($img)) {
                $images[] = $img;
            }

            if (empty($images)) {
                $this->debug("未提取到图片");
                return null;
            }

            $this->debug("成功提取图片: " . count($images) . " 张");

            // 构造返回数据
            return [
                'title' => $imgjson['photo']['caption'] ?? '',
                'desc' => $imgjson['photo']['caption'] ?? '',
                'author' => $imgjson['photo']['userName'] ?? '',
                'author_id' => $imgjson['photo']['userId'] ?? '',
                'avatar' => $imgjson['photo']['headUrl'] ?? '',
                'cover' => $images[0] ?? '',
                'images' => $images,
                'music' => $music,
                'like' => $imgjson['photo']['likeCount'] ?? 0,
                'comment' => $imgjson['photo']['commentCount'] ?? 0,
                'view' => $imgjson['photo']['viewCount'] ?? 0,
                'time' => $imgjson['photo']['timestamp'] ?? 0,
                'type' => 'image'
            ];

        } catch (Exception $e) {
            $this->debug("图集解析异常: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 按原项目逻辑解析视频
     */
    private function parseAsVideoOriginal($originalUrl, $finalUrl)
    {
        try {
            $this->debug("使用原项目逻辑解析视频");
            
            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36'
            ];

            // 提取ID
            $shortVideoPattern = '/short-video\/([^?]+)/';
            $photoPattern = '/photo\/([^?]+)/';
            
            $id = null;
            if (preg_match($shortVideoPattern, $finalUrl, $matches)) {
                $id = $matches[1];
                $requestUrl = $originalUrl;
            } elseif (preg_match($photoPattern, $finalUrl, $matches)) {
                $id = $matches[1];
                $requestUrl = "https://www.kuaishou.com/short-video/{$id}";
            }

            if (!$id) {
                $this->debug("无法提取视频ID");
                return null;
            }

            $this->debug("提取到视频ID: $id");

            $response = $this->httpClient->get($requestUrl, $headers, 'desktop');
            if (!$response) {
                $this->debug("HTTP请求失败");
                return null;
            }

            // 使用APOLLO_STATE模式
            $apolloStatePattern = '/window\.__APOLLO_STATE__\s*=\s*(.*?)\<\/script>/s';
            if (!preg_match($apolloStatePattern, $response, $matches)) {
                $this->debug("未找到APOLLO_STATE数据");
                return null;
            }

            $jsonString = stripslashes($matches[1]);
            
            // 清理JavaScript函数
            $jsonString = preg_replace('/function\s*\([^)]*\)\s*{[^}]*}/', ':', $jsonString);
            $jsonString = preg_replace('/,\s*(?=}|])/', '', $jsonString);
            $jsonString = str_replace(';(:());', '', $jsonString);

            $data = json_decode($jsonString, true);
            if (json_last_error() != JSON_ERROR_NONE) {
                $this->debug("JSON解析失败: " . json_last_error_msg());
                return null;
            }

            $key = "VisionVideoDetailPhoto:$id";
            if (!isset($data['defaultClient'][$key])) {
                $this->debug("未找到视频数据键: $key");
                return null;
            }

            $itemData = $data['defaultClient'][$key];

            return [
                'title' => $itemData['caption'] ?? '',
                'desc' => $itemData['caption'] ?? '',
                'author' => $itemData['userName'] ?? '',
                'author_id' => $itemData['userId'] ?? '',
                'avatar' => $itemData['headUrl'] ?? '',
                'cover' => $itemData['coverUrl'] ?? '',
                'url' => $itemData['photoUrl'] ?? '',
                'like' => $itemData['likeCount'] ?? 0,
                'comment' => $itemData['commentCount'] ?? 0,
                'view' => $itemData['viewCount'] ?? 0,
                'time' => $itemData['timestamp'] ?? 0,
                'duration' => $itemData['duration'] ?? 0,
                'width' => $itemData['width'] ?? 0,
                'height' => $itemData['height'] ?? 0,
                'type' => 'video'
            ];

        } catch (Exception $e) {
            $this->debug("视频解析异常: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取重定向URL
     */
    private function getRedirectUrl($url)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_NOBODY => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30
        ]);

        curl_exec($ch);
        $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        curl_close($ch);

        return $finalUrl ?: $url;
    }

    /**
     * 原项目的cleanInvalidJsonEscapes方法
     */
    private function cleanInvalidJsonEscapes($jsonStr)
    {
        // 处理非法Unicode转义序列
        $jsonStr = preg_replace_callback(
            '/\\\\u([0-9a-fA-F]{0,4})([0-9a-fA-F]*)([^0-9a-fA-F].*?)(?=\\\\u|$)/',
            function($matches) {
                $validPart = '';
                $extraPart = '';

                if (strlen($matches[1]) === 4) {
                    $validPart = '\\u' . $matches[1];
                    $extraPart = $matches[2] . $matches[3];
                } elseif (strlen($matches[1]) + strlen($matches[2]) >= 4) {
                    $hexChars = $matches[1] . $matches[2];
                    $validPart = '\\u' . substr($hexChars, 0, 4);
                    $extraPart = substr($hexChars, 4) . $matches[3];
                } else {
                    $extraPart = $matches[1] . $matches[2] . $matches[3];
                }

                return $validPart . (empty($extraPart) ? '' : ' ' . $extraPart);
            },
            $jsonStr
        );

        // 移除剩余的非法转义字符
        $jsonStr = preg_replace('/\\\\([^"\\/bfnrtu])/', '$1', $jsonStr);
        
        // 修复单引号为双引号
        $jsonStr = str_replace("'", '"', $jsonStr);
        
        // 移除多余的分号
        $jsonStr = preg_replace('/;([^"]*")/', '$1', $jsonStr);

        return $jsonStr;
    }

    /**
     * 兼容方法
     */
    protected function extractVideoId($url)
    {
        return null; // 备用版本不使用此方法
    }

    protected function getVideoInfo($videoId)
    {
        return null; // 备用版本不使用此方法
    }
}
?>
