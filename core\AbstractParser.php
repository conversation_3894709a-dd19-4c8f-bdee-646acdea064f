<?php
/**
 * 抽象解析器基类
 * 定义统一的解析接口和数据格式
 */

require_once __DIR__ . '/../utils/HttpClient.php';
require_once __DIR__ . '/../utils/Logger.php';

abstract class AbstractParser
{
    protected $httpClient;
    protected $logger;
    protected $platform;

    public function __construct($platform)
    {
        $this->platform = $platform;
        $this->httpClient = new HttpClient();
        $this->logger = new Logger();
    }

    /**
     * 解析视频信息 - 抽象方法，子类必须实现
     */
    abstract public function parse($url);

    /**
     * 提取视频ID - 抽象方法，子类必须实现
     */
    abstract protected function extractVideoId($url);

    /**
     * 获取视频信息 - 抽象方法，子类必须实现
     */
    abstract protected function getVideoInfo($videoId);

    /**
     * 标准化数据格式 - 统一版本
     */
    protected function formatData($rawData)
    {
        // 优先检查是否有视频URL来判断内容类型
        $hasVideoUrl = !empty($rawData['url']) || !empty($rawData['video_url']);
        $images = $this->formatImages($rawData['images'] ?? $rawData['imgurl'] ?? []);

        // 根据原始数据的type字段或视频URL存在性来判断类型
        $contentType = $rawData['type'] ?? ($hasVideoUrl ? 'video' : 'image');
        $isVideoContent = ($contentType === 'video');

        // 处理视频URL
        $videoUrl = '';
        if ($isVideoContent && $hasVideoUrl) {
            $videoUrl = $this->sanitizeUrl($rawData['url'] ?? $rawData['video_url'] ?? '');
        } else {
            $videoUrl = '当前为图文解析，无视频链接';
        }

        $standardData = [
            // 基础信息
            'platform' => $this->platform,
            'type' => $contentType,
            'title' => $this->sanitizeString($rawData['title'] ?? ''),
            'description' => $this->sanitizeString($rawData['desc'] ?? $rawData['description'] ?? ''),

            // 作者信息
            'author' => [
                'name' => $this->sanitizeString($rawData['author'] ?? $rawData['author_name'] ?? ''),
                'id' => $this->sanitizeString($rawData['author_id'] ?? $rawData['uid'] ?? ''),
                'avatar' => $this->sanitizeUrl($rawData['avatar'] ?? $rawData['author_avatar'] ?? ''),
                'follower_count' => $this->sanitizeNumber($rawData['follower_count'] ?? 0)
            ],

            // 视频信息（保持原有结构兼容性）
            'video' => [
                'url' => $videoUrl,
                'backup_url' => $isVideoContent ? $this->sanitizeUrl($rawData['backup_url'] ?? '') : '',
                'duration' => $isVideoContent ? $this->sanitizeNumber($rawData['duration'] ?? 0) : 0,
                'width' => $this->sanitizeNumber($rawData['width'] ?? 0),
                'height' => $this->sanitizeNumber($rawData['height'] ?? 0),
                'size' => $this->sanitizeNumber($rawData['video_size'] ?? 0),
                'bitrate' => $this->sanitizeNumber($rawData['bitrate'] ?? 0)
            ],

            // 图集信息
            'images' => $images,
            'image_count' => count($images),

            // 音乐信息
            'music' => $this->formatMusic($rawData['music'] ?? []),

            // 统计信息
            'statistics' => [
                'like_count' => $this->sanitizeNumber($rawData['like'] ?? $rawData['like_count'] ?? $rawData['digg_count'] ?? 0),
                'comment_count' => $this->sanitizeNumber($rawData['comment'] ?? $rawData['comment_count'] ?? 0),
                'share_count' => $this->sanitizeNumber($rawData['share'] ?? $rawData['share_count'] ?? 0),
                'view_count' => $this->sanitizeNumber($rawData['view'] ?? $rawData['view_count'] ?? $rawData['play_count'] ?? 0),
                'collect_count' => $this->sanitizeNumber($rawData['collect'] ?? $rawData['collect_count'] ?? 0)
            ],

            // 时间和来源
            'create_time' => $this->formatTime($rawData['time'] ?? $rawData['create_time'] ?? $rawData['publish_time'] ?? 0),
            'source_url' => $rawData['source_url'] ?? '',

            // 额外信息
            'tags' => $this->formatTags($rawData['tags'] ?? []),
            'location' => $this->sanitizeString($rawData['location'] ?? ''),
            'extra' => $rawData['extra'] ?? []
        ];

        return $standardData;
    }

    /**
     * 格式化图片数组
     */
    protected function formatImages($images)
    {
        if (!is_array($images)) {
            return [];
        }

        $formattedImages = [];
        foreach ($images as $image) {
            if (is_string($image)) {
                $formattedImages[] = $this->sanitizeUrl($image);
            } elseif (is_array($image) && isset($image['url'])) {
                $formattedImages[] = $this->sanitizeUrl($image['url']);
            }
        }

        return array_filter($formattedImages);
    }

    /**
     * 格式化音乐信息
     */
    protected function formatMusic($music)
    {
        if (!is_array($music)) {
            return [];
        }

        return [
            'title' => $this->sanitizeString($music['title'] ?? ''),
            'author' => $this->sanitizeString($music['author'] ?? ''),
            'url' => $this->sanitizeUrl($music['url'] ?? ''),
            'cover' => $this->sanitizeUrl($music['cover'] ?? $music['avatar'] ?? '')
        ];
    }

    /**
     * 格式化标签信息
     */
    protected function formatTags($tagsData)
    {
        if (empty($tagsData)) {
            return [];
        }

        if (is_string($tagsData)) {
            // 如果是字符串，按逗号或空格分割
            return array_filter(array_map('trim', preg_split('/[,\s]+/', $tagsData)));
        }

        if (is_array($tagsData)) {
            return array_filter(array_map(function($tag) {
                return is_string($tag) ? trim($tag) : (string)$tag;
            }, $tagsData));
        }

        return [];
    }

    /**
     * 格式化时间
     */
    protected function formatTime($timestamp)
    {
        if (empty($timestamp)) {
            return '';
        }

        // 如果是字符串，尝试转换为时间戳
        if (is_string($timestamp)) {
            $timestamp = strtotime($timestamp);
        }

        // 如果时间戳长度为13位（毫秒），转换为10位（秒）
        if (strlen((string)$timestamp) === 13) {
            $timestamp = intval($timestamp / 1000);
        }

        return date('Y-m-d H:i:s', $timestamp);
    }

    /**
     * 清理字符串
     */
    protected function sanitizeString($string)
    {
        if (!is_string($string)) {
            return '';
        }
        
        // 移除HTML标签
        $string = strip_tags($string);
        
        // 移除多余的空白字符
        $string = preg_replace('/\s+/', ' ', $string);
        
        // 去除首尾空格
        return trim($string);
    }

    /**
     * 清理URL
     */
    protected function sanitizeUrl($url)
    {
        if (!is_string($url) || empty($url)) {
            return '';
        }

        // 基本URL验证
        if (!filter_var($url, FILTER_VALIDATE_URL)) {
            // 如果不是完整URL，尝试补全协议
            if (strpos($url, '//') === 0) {
                $url = 'https:' . $url;
            } elseif (strpos($url, '/') === 0) {
                // 相对路径，需要根据平台补全
                return $url;
            } else {
                return '';
            }
        }

        return $url;
    }

    /**
     * 清理数字
     */
    protected function sanitizeNumber($number)
    {
        if (is_numeric($number)) {
            return intval($number);
        }
        return 0;
    }

    /**
     * 去除视频水印
     */
    protected function removeWatermark($videoUrl)
    {
        // 抖音去水印
        if (strpos($videoUrl, 'playwm') !== false) {
            return str_replace('playwm', 'play', $videoUrl);
        }

        // 快手去水印
        if (strpos($videoUrl, 'watermark') !== false) {
            return str_replace('watermark', '', $videoUrl);
        }

        return $videoUrl;
    }

    /**
     * 处理错误
     */
    protected function handleError($message, $code = 404)
    {
        $this->logger->error("[$this->platform] $message");
        return null;
    }

    /**
     * 记录调试信息
     */
    protected function debug($message, $context = [])
    {
        $logMessage = "[$this->platform] $message";

        // 使用logger记录
        $this->logger->debug($logMessage, $context);

        // 同时写入调试文件（用于实时调试）
        $debugFile = __DIR__ . '/../logs/debug.log';
        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' | Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
        file_put_contents($debugFile, "[$timestamp] $logMessage$contextStr\n", FILE_APPEND | LOCK_EX);
    }

    /**
     * 记录信息
     */
    protected function info($message, $context = [])
    {
        $this->logger->info("[$this->platform] $message", $context);
    }
}
?>
