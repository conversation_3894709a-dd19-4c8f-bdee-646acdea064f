<?php
/**
 * 抖音解析器
 */

class DouyinParser extends AbstractParser
{
    /**
     * 解析抖音视频
     */
    public function parse($url)
    {
        try {
            $this->info("开始解析抖音URL: $url");
            
            // 提取视频ID
            $videoId = $this->extractVideoId($url);
            if (!$videoId) {
                return $this->handleError('无法提取视频ID');
            }

            $this->debug("提取到视频ID: $videoId");

            // 获取视频信息
            $videoInfo = $this->getVideoInfo($videoId);
            if (!$videoInfo) {
                return $this->handleError('获取视频信息失败');
            }

            // 格式化数据
            $formattedData = $this->formatData($videoInfo);
            $formattedData['source_url'] = $url;

            $this->info("抖音视频解析成功");
            return $formattedData;

        } catch (Exception $e) {
            return $this->handleError('解析过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * 提取视频ID
     */
    protected function extractVideoId($url)
    {
        // 获取重定向后的URL
        try {
            $headers = $this->httpClient->getHeaders($url);
            $finalUrl = $url;
            
            if (isset($headers['Location'])) {
                $location = is_array($headers['Location']) ? end($headers['Location']) : $headers['Location'];
                if ($location) {
                    $finalUrl = $location;
                }
            }
        } catch (Exception $e) {
            $finalUrl = $url;
        }

        // 从URL中提取ID
        $patterns = [
            '/video\/(\d+)/',           // 标准格式
            '/\/(\d{19})/',             // 19位数字ID
            '/\/(\d{18,20})/',          // 18-20位数字ID
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $finalUrl, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }

    /**
     * 获取视频信息
     */
    protected function getVideoInfo($videoId)
    {
        try {
            $url = "https://www.iesdouyin.com/share/video/$videoId";
            $headers = [
                'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
            ];

            $response = $this->httpClient->get($url, $headers, 'mobile');
            if (!$response) {
                return null;
            }

            // 提取页面中的JSON数据
            $pattern = '/window\._ROUTER_DATA\s*=\s*(.*?)\<\/script>/s';
            if (!preg_match($pattern, $response, $matches)) {
                return null;
            }

            $jsonData = json_decode(trim($matches[1]), true);
            if (!isset($jsonData['loaderData'])) {
                return null;
            }

            $itemData = $jsonData['loaderData']['video_(id)/page']['videoInfoRes']['item_list'][0] ?? null;
            if (!$itemData) {
                return null;
            }

            // 处理视频URL（去水印）
            $videoUrl = '';
            if (isset($itemData['video']['play_addr']['url_list'][0])) {
                $videoUrl = str_replace('playwm', 'play', $itemData['video']['play_addr']['url_list'][0]);
            }

            // 处理图集
            $images = [];
            if (isset($itemData['images']) && is_array($itemData['images'])) {
                foreach ($itemData['images'] as $image) {
                    if (isset($image['url_list'][0])) {
                        $images[] = $image['url_list'][0];
                    }
                }
            }

            // 构造标准化数据
            $data = [
                'title' => $itemData['desc'] ?? '',
                'desc' => $itemData['desc'] ?? '',
                'author' => $itemData['author']['nickname'] ?? '',
                'author_id' => $itemData['author']['unique_id'] ?? '',
                'uid' => $itemData['author']['unique_id'] ?? '',
                'avatar' => $itemData['author']['avatar_medium']['url_list'][0] ?? '',
                'cover' => $itemData['video']['cover']['url_list'][0] ?? '',
                'url' => $videoUrl,
                'images' => $images,
                'like' => $itemData['statistics']['digg_count'] ?? 0,
                'comment' => $itemData['statistics']['comment_count'] ?? 0,
                'share' => $itemData['statistics']['share_count'] ?? 0,
                'time' => $itemData['create_time'] ?? 0,
                'duration' => $itemData['video']['duration'] ?? 0,
                'width' => $itemData['video']['width'] ?? 0,
                'height' => $itemData['video']['height'] ?? 0,
                'music' => [
                    'title' => $itemData['music']['title'] ?? '',
                    'author' => $itemData['music']['author'] ?? '',
                    'url' => $itemData['music']['play_url']['url_list'][0] ?? '',
                    'cover' => $itemData['music']['cover_large']['url_list'][0] ?? ''
                ]
            ];

            return $data;

        } catch (Exception $e) {
            $this->debug("获取抖音视频信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 格式化抖音特有数据
     */
    protected function formatData($rawData)
    {
        $standardData = parent::formatData($rawData);
        
        // 抖音特有的处理
        if (!empty($rawData['images'])) {
            $standardData['video']['url'] = '当前为图文解析，图文数量为:' . count($rawData['images']) . '张图片';
        }

        return $standardData;
    }
}
?>
