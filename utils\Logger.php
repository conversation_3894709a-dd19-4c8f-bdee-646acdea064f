<?php
/**
 * 日志管理器
 */

class Logger
{
    private $logDir;
    private $logLevel;
    private $levels = [
        'DEBUG' => 0,
        'INFO' => 1,
        'WARN' => 2,
        'ERROR' => 3
    ];

    public function __construct()
    {
        $this->logDir = LOG_DIR;
        $this->logLevel = LOG_LEVEL;
    }

    /**
     * 记录调试信息
     */
    public function debug($message, $context = [])
    {
        $this->log('DEBUG', $message, $context);
    }

    /**
     * 记录一般信息
     */
    public function info($message, $context = [])
    {
        $this->log('INFO', $message, $context);
    }

    /**
     * 记录警告信息
     */
    public function warn($message, $context = [])
    {
        $this->log('WARN', $message, $context);
    }

    /**
     * 记录错误信息
     */
    public function error($message, $context = [])
    {
        $this->log('ERROR', $message, $context);
    }

    /**
     * 通用日志记录方法
     */
    private function log($level, $message, $context = [])
    {
        if (!LOG_ENABLE) {
            return;
        }

        // 检查日志级别
        if ($this->levels[$level] < $this->levels[$this->logLevel]) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $logFile = $this->logDir . date('Y-m-d') . '.log';
        
        $contextStr = '';
        if (!empty($context)) {
            $contextStr = ' | Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE);
        }

        $logEntry = "[$timestamp] [$level] $message$contextStr" . PHP_EOL;
        
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * 清理过期日志
     */
    public function cleanOldLogs($days = 30)
    {
        $files = glob($this->logDir . '*.log');
        $cutoff = time() - ($days * 24 * 60 * 60);
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff) {
                unlink($file);
            }
        }
    }
}
?>
