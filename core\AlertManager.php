<?php
/**
 * 告警管理器
 * 监控系统指标并发送告警通知
 */

class AlertManager
{
    private $logger;
    private $db;
    private $alertRules = [];
    private $notificationChannels = [];

    public function __construct($db = null)
    {
        $this->logger = new Logger();
        $this->db = $db;
        $this->initAlertRules();
        $this->initNotificationChannels();
    }

    /**
     * 初始化告警规则
     */
    private function initAlertRules()
    {
        $this->alertRules = [
            'high_error_rate' => [
                'name' => '错误率过高',
                'threshold' => 10, // 10%
                'severity' => 'critical',
                'check_interval' => 300, // 5分钟
                'enabled' => true
            ],
            'slow_response' => [
                'name' => '响应时间过慢',
                'threshold' => 5000, // 5秒
                'severity' => 'warning',
                'check_interval' => 300,
                'enabled' => true
            ],
            'high_memory_usage' => [
                'name' => '内存使用率过高',
                'threshold' => 80, // 80%
                'severity' => 'warning',
                'check_interval' => 600, // 10分钟
                'enabled' => true
            ],
            'disk_space_low' => [
                'name' => '磁盘空间不足',
                'threshold' => 90, // 90%
                'severity' => 'critical',
                'check_interval' => 1800, // 30分钟
                'enabled' => true
            ],
            'api_down' => [
                'name' => 'API服务异常',
                'threshold' => 1, // 1次失败
                'severity' => 'critical',
                'check_interval' => 60, // 1分钟
                'enabled' => true
            ]
        ];
    }

    /**
     * 初始化通知渠道
     */
    private function initNotificationChannels()
    {
        $this->notificationChannels = [
            'email' => [
                'enabled' => defined('ALERT_EMAIL_ENABLE') && ALERT_EMAIL_ENABLE,
                'recipients' => explode(',', ALERT_EMAIL_RECIPIENTS ?? ''),
                'smtp_host' => ALERT_SMTP_HOST ?? 'localhost',
                'smtp_port' => ALERT_SMTP_PORT ?? 587,
                'smtp_user' => ALERT_SMTP_USER ?? '',
                'smtp_pass' => ALERT_SMTP_PASS ?? ''
            ],
            'webhook' => [
                'enabled' => defined('ALERT_WEBHOOK_ENABLE') && ALERT_WEBHOOK_ENABLE,
                'url' => ALERT_WEBHOOK_URL ?? '',
                'secret' => ALERT_WEBHOOK_SECRET ?? ''
            ],
            'sms' => [
                'enabled' => defined('ALERT_SMS_ENABLE') && ALERT_SMS_ENABLE,
                'api_key' => ALERT_SMS_API_KEY ?? '',
                'phones' => explode(',', ALERT_SMS_PHONES ?? '')
            ]
        ];
    }

    /**
     * 检查所有告警规则
     */
    public function checkAlerts()
    {
        foreach ($this->alertRules as $ruleId => $rule) {
            if (!$rule['enabled']) {
                continue;
            }

            // 检查是否到了检查时间
            if (!$this->shouldCheck($ruleId, $rule['check_interval'])) {
                continue;
            }

            try {
                $triggered = $this->checkRule($ruleId, $rule);
                if ($triggered) {
                    $this->triggerAlert($ruleId, $rule, $triggered);
                }
            } catch (Exception $e) {
                $this->logger->error("检查告警规则失败: {$ruleId}, " . $e->getMessage());
            }
        }
    }

    /**
     * 检查是否应该执行检查
     */
    private function shouldCheck($ruleId, $interval)
    {
        $lastCheck = $this->getLastCheckTime($ruleId);
        return (time() - $lastCheck) >= $interval;
    }

    /**
     * 检查具体规则
     */
    private function checkRule($ruleId, $rule)
    {
        switch ($ruleId) {
            case 'high_error_rate':
                return $this->checkErrorRate($rule['threshold']);
            
            case 'slow_response':
                return $this->checkResponseTime($rule['threshold']);
            
            case 'high_memory_usage':
                return $this->checkMemoryUsage($rule['threshold']);
            
            case 'disk_space_low':
                return $this->checkDiskSpace($rule['threshold']);
            
            case 'api_down':
                return $this->checkApiHealth();
            
            default:
                return false;
        }
    }

    /**
     * 检查错误率
     */
    private function checkErrorRate($threshold)
    {
        if (!$this->db) return false;

        $sql = "SELECT 
                    SUM(total_requests) as total,
                    SUM(failed_requests) as failed
                FROM api_statistics 
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL 1 HOUR)";
        
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();

        if ($result['total'] > 0) {
            $errorRate = ($result['failed'] / $result['total']) * 100;
            if ($errorRate > $threshold) {
                return [
                    'current_value' => round($errorRate, 2),
                    'threshold' => $threshold,
                    'message' => "错误率 {$errorRate}% 超过阈值 {$threshold}%"
                ];
            }
        }

        return false;
    }

    /**
     * 检查响应时间
     */
    private function checkResponseTime($threshold)
    {
        if (!$this->db) return false;

        $sql = "SELECT AVG(avg_response_time) as avg_time
                FROM api_statistics 
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL 1 HOUR)";
        
        $stmt = $this->db->pdo->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();

        $avgTime = $result['avg_time'] ?? 0;
        if ($avgTime > $threshold) {
            return [
                'current_value' => round($avgTime, 2),
                'threshold' => $threshold,
                'message' => "平均响应时间 {$avgTime}ms 超过阈值 {$threshold}ms"
            ];
        }

        return false;
    }

    /**
     * 检查内存使用率
     */
    private function checkMemoryUsage($threshold)
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        if ($memoryLimit > 0) {
            $usagePercent = ($memoryUsage / $memoryLimit) * 100;
            if ($usagePercent > $threshold) {
                return [
                    'current_value' => round($usagePercent, 2),
                    'threshold' => $threshold,
                    'message' => "内存使用率 {$usagePercent}% 超过阈值 {$threshold}%"
                ];
            }
        }

        return false;
    }

    /**
     * 检查磁盘空间
     */
    private function checkDiskSpace($threshold)
    {
        $total = disk_total_space('.');
        $free = disk_free_space('.');
        
        if ($total > 0) {
            $usagePercent = (($total - $free) / $total) * 100;
            if ($usagePercent > $threshold) {
                return [
                    'current_value' => round($usagePercent, 2),
                    'threshold' => $threshold,
                    'message' => "磁盘使用率 {$usagePercent}% 超过阈值 {$threshold}%"
                ];
            }
        }

        return false;
    }

    /**
     * 检查API健康状态
     */
    private function checkApiHealth()
    {
        try {
            // 简单的健康检查
            $response = file_get_contents('http://localhost/admin/status.php');
            $data = json_decode($response, true);
            
            if (!$data || !isset($data['api_info'])) {
                return [
                    'current_value' => 'DOWN',
                    'threshold' => 'UP',
                    'message' => 'API健康检查失败'
                ];
            }
        } catch (Exception $e) {
            return [
                'current_value' => 'DOWN',
                'threshold' => 'UP',
                'message' => 'API无法访问: ' . $e->getMessage()
            ];
        }

        return false;
    }

    /**
     * 触发告警
     */
    private function triggerAlert($ruleId, $rule, $data)
    {
        $alert = [
            'id' => uniqid('alert_'),
            'rule_id' => $ruleId,
            'rule_name' => $rule['name'],
            'severity' => $rule['severity'],
            'message' => $data['message'],
            'current_value' => $data['current_value'],
            'threshold' => $data['threshold'],
            'timestamp' => time(),
            'hostname' => gethostname(),
            'service' => 'video-parser-api'
        ];

        // 记录告警
        $this->recordAlert($alert);

        // 发送通知
        $this->sendNotifications($alert);

        // 更新检查时间
        $this->updateLastCheckTime($ruleId);
    }

    /**
     * 记录告警
     */
    private function recordAlert($alert)
    {
        $this->logger->warn("ALERT: {$alert['rule_name']} - {$alert['message']}", $alert);

        if ($this->db) {
            try {
                $sql = "INSERT INTO alerts (alert_id, rule_id, rule_name, severity, message, current_value, threshold, created_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
                $stmt = $this->db->pdo->prepare($sql);
                $stmt->execute([
                    $alert['id'],
                    $alert['rule_id'],
                    $alert['rule_name'],
                    $alert['severity'],
                    $alert['message'],
                    $alert['current_value'],
                    $alert['threshold']
                ]);
            } catch (Exception $e) {
                $this->logger->error("记录告警失败: " . $e->getMessage());
            }
        }
    }

    /**
     * 发送通知
     */
    private function sendNotifications($alert)
    {
        foreach ($this->notificationChannels as $channel => $config) {
            if (!$config['enabled']) {
                continue;
            }

            try {
                switch ($channel) {
                    case 'email':
                        $this->sendEmailNotification($alert, $config);
                        break;
                    case 'webhook':
                        $this->sendWebhookNotification($alert, $config);
                        break;
                    case 'sms':
                        $this->sendSmsNotification($alert, $config);
                        break;
                }
            } catch (Exception $e) {
                $this->logger->error("发送{$channel}通知失败: " . $e->getMessage());
            }
        }
    }

    /**
     * 发送邮件通知
     */
    private function sendEmailNotification($alert, $config)
    {
        if (empty($config['recipients'])) {
            return;
        }

        $subject = "[{$alert['severity']}] {$alert['rule_name']} - {$alert['service']}";
        $body = $this->formatEmailBody($alert);

        foreach ($config['recipients'] as $recipient) {
            if (empty(trim($recipient))) continue;
            
            // 这里可以使用PHPMailer或其他邮件库
            mail(trim($recipient), $subject, $body, [
                'From' => '<EMAIL>',
                'Content-Type' => 'text/html; charset=UTF-8'
            ]);
        }
    }

    /**
     * 发送Webhook通知
     */
    private function sendWebhookNotification($alert, $config)
    {
        if (empty($config['url'])) {
            return;
        }

        $payload = json_encode($alert);
        $signature = hash_hmac('sha256', $payload, $config['secret']);

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $config['url'],
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'X-Signature: sha256=' . $signature
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10
        ]);

        curl_exec($ch);
        curl_close($ch);
    }

    /**
     * 发送短信通知
     */
    private function sendSmsNotification($alert, $config)
    {
        // 这里可以集成短信服务商API
        // 如阿里云短信、腾讯云短信等
    }

    /**
     * 格式化邮件内容
     */
    private function formatEmailBody($alert)
    {
        return "
        <html>
        <body>
            <h2>🚨 系统告警通知</h2>
            <table border='1' cellpadding='10'>
                <tr><td><strong>告警名称</strong></td><td>{$alert['rule_name']}</td></tr>
                <tr><td><strong>严重级别</strong></td><td>{$alert['severity']}</td></tr>
                <tr><td><strong>告警信息</strong></td><td>{$alert['message']}</td></tr>
                <tr><td><strong>当前值</strong></td><td>{$alert['current_value']}</td></tr>
                <tr><td><strong>阈值</strong></td><td>{$alert['threshold']}</td></tr>
                <tr><td><strong>服务</strong></td><td>{$alert['service']}</td></tr>
                <tr><td><strong>主机</strong></td><td>{$alert['hostname']}</td></tr>
                <tr><td><strong>时间</strong></td><td>" . date('Y-m-d H:i:s', $alert['timestamp']) . "</td></tr>
            </table>
            <p>请及时处理此告警。</p>
        </body>
        </html>
        ";
    }

    /**
     * 获取最后检查时间
     */
    private function getLastCheckTime($ruleId)
    {
        $file = CACHE_DIR . "alert_check_{$ruleId}.txt";
        if (file_exists($file)) {
            return intval(file_get_contents($file));
        }
        return 0;
    }

    /**
     * 更新最后检查时间
     */
    private function updateLastCheckTime($ruleId)
    {
        $file = CACHE_DIR . "alert_check_{$ruleId}.txt";
        file_put_contents($file, time());
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit($limit)
    {
        if ($limit === '-1') return 0;
        
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;
        
        switch ($last) {
            case 'g': $limit *= 1024;
            case 'm': $limit *= 1024;
            case 'k': $limit *= 1024;
        }
        
        return $limit;
    }
}
?>
