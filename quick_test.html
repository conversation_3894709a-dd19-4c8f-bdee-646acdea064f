<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-form {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .test-form input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-form button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .quick-link {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .quick-link h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .quick-link .url {
            font-family: monospace;
            font-size: 12px;
            color: #6c757d;
            word-break: break-all;
            margin-bottom: 10px;
        }
        .quick-link button {
            background: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 5px;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
        .loading { border-color: #ffc107; background: #fff3cd; }
        .tools {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .tools a {
            padding: 8px 15px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .tools a:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 快速调试测试</h1>
        
        <div class="tools">
            <a href="debug.php" target="_blank">详细调试工具</a>
            <a href="view_logs.php" target="_blank">实时日志查看</a>
            <a href="test_page.html" target="_blank">完整测试页面</a>
        </div>

        <div class="test-form">
            <input type="text" id="testUrl" placeholder="输入要测试的URL">
            <button onclick="testUrl()">测试</button>
        </div>

        <div class="quick-links">
            <div class="quick-link">
                <h4>🎯 快手短链接（问题URL）</h4>
                <div class="url">https://v.kuaishou.com/ns3MMLr3</div>
                <button onclick="testQuickUrl('https://v.kuaishou.com/ns3MMLr3')">测试API</button>
                <button onclick="debugQuickUrl('https://v.kuaishou.com/ns3MMLr3')">详细调试</button>
            </div>

            <div class="quick-link">
                <h4>🎯 小红书视频（问题URL）</h4>
                <div class="url">http://xhslink.com/m/15e8F81IBxv</div>
                <button onclick="testQuickUrl('http://xhslink.com/m/15e8F81IBxv')">测试API</button>
                <button onclick="debugQuickUrl('http://xhslink.com/m/15e8F81IBxv')">详细调试</button>
            </div>

            <div class="quick-link">
                <h4>✅ 抖音视频（对照测试）</h4>
                <div class="url">https://v.douyin.com/iJsLmBaE/</div>
                <button onclick="testQuickUrl('https://v.douyin.com/iJsLmBaE/')">测试API</button>
                <button onclick="debugQuickUrl('https://v.douyin.com/iJsLmBaE/')">详细调试</button>
            </div>

            <div class="quick-link">
                <h4>✅ 哔哩哔哩视频（对照测试）</h4>
                <div class="url">https://www.bilibili.com/video/BV1xx411c7mu</div>
                <button onclick="testQuickUrl('https://www.bilibili.com/video/BV1xx411c7mu')">测试API</button>
                <button onclick="debugQuickUrl('https://www.bilibili.com/video/BV1xx411c7mu')">详细调试</button>
            </div>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        function testUrl() {
            const url = document.getElementById('testUrl').value.trim();
            if (!url) {
                alert('请输入URL');
                return;
            }
            testQuickUrl(url);
        }

        function testQuickUrl(url) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试 API 接口...\n\n请求URL: ' + url;

            const apiUrl = `index.php?url=${encodeURIComponent(url)}`;
            
            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    resultDiv.className = 'result ' + (data.code === 200 ? 'success' : 'error');
                    
                    let output = `=== API 测试结果 ===\n`;
                    output += `状态码: ${data.code}\n`;
                    output += `消息: ${data.msg}\n`;
                    output += `时间戳: ${data.timestamp}\n\n`;
                    
                    if (data.code === 200 && data.data) {
                        output += `=== 解析结果分析 ===\n`;
                        output += `平台: ${data.data.platform || '未知'}\n`;
                        output += `类型: ${data.data.type || '未知'}\n`;
                        output += `标题: ${data.data.title || '无'}\n`;
                        
                        if (data.data.type === 'video') {
                            output += `视频URL: ${data.data.video?.url || '无'}\n`;
                            output += `视频时长: ${data.data.video?.duration || 0}秒\n`;
                        } else if (data.data.type === 'image') {
                            output += `图片数量: ${data.data.images?.length || 0}\n`;
                        }
                        
                        output += `作者: ${data.data.author?.name || '无'}\n`;
                        output += `点赞数: ${data.data.statistics?.like_count || 0}\n`;
                        output += `评论数: ${data.data.statistics?.comment_count || 0}\n\n`;
                    }
                    
                    output += `=== 完整响应数据 ===\n`;
                    output += JSON.stringify(data, null, 2);
                    
                    resultDiv.textContent = output;
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `请求失败: ${error.message}\n\n这可能是网络问题或服务器错误。`;
                });
        }

        function debugQuickUrl(url) {
            window.open(`debug.php?url=${encodeURIComponent(url)}`, '_blank');
        }

        // 页面加载时的提示
        window.onload = function() {
            console.log('调试提示:');
            console.log('1. 点击"测试API"查看接口返回结果');
            console.log('2. 点击"详细调试"查看解析过程');
            console.log('3. 使用"实时日志查看"监控调试信息');
            console.log('4. 重点关注快手和小红书的问题URL');
        };
    </script>
</body>
</html>
