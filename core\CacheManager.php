<?php
/**
 * 缓存管理器
 * 提供简单的文件缓存功能
 */

class CacheManager
{
    private $cacheDir;
    private $enabled;

    public function __construct()
    {
        $this->cacheDir = CACHE_DIR;
        $this->enabled = CACHE_ENABLE;
    }

    /**
     * 获取缓存
     */
    public function get($key)
    {
        if (!$this->enabled) {
            return null;
        }

        $filePath = $this->getCacheFilePath($key);
        
        if (!file_exists($filePath)) {
            return null;
        }

        $data = file_get_contents($filePath);
        if ($data === false) {
            return null;
        }

        $cacheData = json_decode($data, true);
        if (!$cacheData || !isset($cacheData['expire_time']) || !isset($cacheData['data'])) {
            return null;
        }

        // 检查是否过期
        if (time() > $cacheData['expire_time']) {
            $this->delete($key);
            return null;
        }

        return $cacheData['data'];
    }

    /**
     * 设置缓存
     */
    public function set($key, $data, $expireTime = null)
    {
        if (!$this->enabled) {
            return false;
        }

        if ($expireTime === null) {
            $expireTime = CACHE_EXPIRE_TIME;
        }

        $cacheData = [
            'data' => $data,
            'expire_time' => time() + $expireTime,
            'create_time' => time()
        ];

        $filePath = $this->getCacheFilePath($key);
        $result = file_put_contents($filePath, json_encode($cacheData), LOCK_EX);
        
        return $result !== false;
    }

    /**
     * 删除缓存
     */
    public function delete($key)
    {
        if (!$this->enabled) {
            return false;
        }

        $filePath = $this->getCacheFilePath($key);
        
        if (file_exists($filePath)) {
            return unlink($filePath);
        }
        
        return true;
    }

    /**
     * 清空所有缓存
     */
    public function clear()
    {
        if (!$this->enabled) {
            return false;
        }

        $files = glob($this->cacheDir . '*.cache');
        $success = true;
        
        foreach ($files as $file) {
            if (!unlink($file)) {
                $success = false;
            }
        }
        
        return $success;
    }

    /**
     * 清理过期缓存
     */
    public function cleanExpired()
    {
        if (!$this->enabled) {
            return false;
        }

        $files = glob($this->cacheDir . '*.cache');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $data = file_get_contents($file);
            if ($data === false) {
                continue;
            }

            $cacheData = json_decode($data, true);
            if (!$cacheData || !isset($cacheData['expire_time'])) {
                unlink($file);
                $cleaned++;
                continue;
            }

            if (time() > $cacheData['expire_time']) {
                unlink($file);
                $cleaned++;
            }
        }
        
        return $cleaned;
    }

    /**
     * 获取缓存文件路径
     */
    private function getCacheFilePath($key)
    {
        $safeKey = preg_replace('/[^a-zA-Z0-9_-]/', '_', $key);
        return $this->cacheDir . $safeKey . '.cache';
    }

    /**
     * 获取缓存统计信息
     */
    public function getStats()
    {
        if (!$this->enabled) {
            return null;
        }

        $files = glob($this->cacheDir . '*.cache');
        $totalSize = 0;
        $validCount = 0;
        $expiredCount = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $data = file_get_contents($file);
            if ($data === false) {
                continue;
            }

            $cacheData = json_decode($data, true);
            if (!$cacheData || !isset($cacheData['expire_time'])) {
                $expiredCount++;
                continue;
            }

            if (time() > $cacheData['expire_time']) {
                $expiredCount++;
            } else {
                $validCount++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_count' => $validCount,
            'expired_count' => $expiredCount,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize)
        ];
    }

    /**
     * 格式化字节大小
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
?>
