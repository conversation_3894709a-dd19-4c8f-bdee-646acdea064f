#!/bin/bash

# AWS部署脚本
# 使用AWS ECS + RDS + ElastiCache部署视频解析API

set -e

# 配置变量
AWS_REGION="us-west-2"
CLUSTER_NAME="video-parser-cluster"
SERVICE_NAME="video-parser-service"
TASK_FAMILY="video-parser-task"
ECR_REPOSITORY="video-parser"
RDS_INSTANCE="video-parser-db"
REDIS_CLUSTER="video-parser-cache"

echo "🚀 开始AWS部署..."

# 1. 构建并推送Docker镜像到ECR
echo "📦 构建Docker镜像..."
docker build -t $ECR_REPOSITORY:latest .

# 获取ECR登录令牌
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com

# 标记并推送镜像
docker tag $ECR_REPOSITORY:latest $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:latest
docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:latest

echo "✅ Docker镜像已推送到ECR"

# 2. 创建RDS实例
echo "🗄️ 创建RDS数据库..."
aws rds create-db-instance \
    --db-instance-identifier $RDS_INSTANCE \
    --db-instance-class db.t3.micro \
    --engine mysql \
    --master-username admin \
    --master-user-password $DB_PASSWORD \
    --allocated-storage 20 \
    --vpc-security-group-ids $DB_SECURITY_GROUP \
    --db-subnet-group-name $DB_SUBNET_GROUP \
    --backup-retention-period 7 \
    --storage-encrypted \
    --region $AWS_REGION

echo "⏳ 等待RDS实例创建完成..."
aws rds wait db-instance-available --db-instance-identifier $RDS_INSTANCE

# 3. 创建ElastiCache Redis集群
echo "🔄 创建Redis缓存集群..."
aws elasticache create-cache-cluster \
    --cache-cluster-id $REDIS_CLUSTER \
    --cache-node-type cache.t3.micro \
    --engine redis \
    --num-cache-nodes 1 \
    --cache-subnet-group-name $CACHE_SUBNET_GROUP \
    --security-group-ids $CACHE_SECURITY_GROUP \
    --region $AWS_REGION

echo "⏳ 等待Redis集群创建完成..."
aws elasticache wait cache-cluster-available --cache-cluster-id $REDIS_CLUSTER

# 4. 创建ECS集群
echo "🏗️ 创建ECS集群..."
aws ecs create-cluster --cluster-name $CLUSTER_NAME --region $AWS_REGION

# 5. 注册任务定义
echo "📋 注册ECS任务定义..."
cat > task-definition.json << EOF
{
    "family": "$TASK_FAMILY",
    "networkMode": "awsvpc",
    "requiresCompatibilities": ["FARGATE"],
    "cpu": "512",
    "memory": "1024",
    "executionRoleArn": "$EXECUTION_ROLE_ARN",
    "taskRoleArn": "$TASK_ROLE_ARN",
    "containerDefinitions": [
        {
            "name": "video-parser",
            "image": "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY:latest",
            "portMappings": [
                {
                    "containerPort": 80,
                    "protocol": "tcp"
                }
            ],
            "environment": [
                {
                    "name": "DB_HOST",
                    "value": "$RDS_ENDPOINT"
                },
                {
                    "name": "DB_NAME",
                    "value": "video_parser"
                },
                {
                    "name": "REDIS_HOST",
                    "value": "$REDIS_ENDPOINT"
                },
                {
                    "name": "CACHE_ENABLE",
                    "value": "true"
                },
                {
                    "name": "DB_ENABLE",
                    "value": "true"
                }
            ],
            "secrets": [
                {
                    "name": "DB_USER",
                    "valueFrom": "$DB_USER_SECRET_ARN"
                },
                {
                    "name": "DB_PASS",
                    "valueFrom": "$DB_PASS_SECRET_ARN"
                }
            ],
            "logConfiguration": {
                "logDriver": "awslogs",
                "options": {
                    "awslogs-group": "/ecs/video-parser",
                    "awslogs-region": "$AWS_REGION",
                    "awslogs-stream-prefix": "ecs"
                }
            },
            "healthCheck": {
                "command": [
                    "CMD-SHELL",
                    "curl -f http://localhost/admin/status.php || exit 1"
                ],
                "interval": 30,
                "timeout": 5,
                "retries": 3,
                "startPeriod": 60
            }
        }
    ]
}
EOF

aws ecs register-task-definition --cli-input-json file://task-definition.json --region $AWS_REGION

# 6. 创建ECS服务
echo "🚀 创建ECS服务..."
aws ecs create-service \
    --cluster $CLUSTER_NAME \
    --service-name $SERVICE_NAME \
    --task-definition $TASK_FAMILY \
    --desired-count 2 \
    --launch-type FARGATE \
    --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_IDS],securityGroups=[$SECURITY_GROUP_IDS],assignPublicIp=ENABLED}" \
    --load-balancers "targetGroupArn=$TARGET_GROUP_ARN,containerName=video-parser,containerPort=80" \
    --region $AWS_REGION

# 7. 创建Application Load Balancer
echo "⚖️ 创建负载均衡器..."
aws elbv2 create-load-balancer \
    --name video-parser-alb \
    --subnets $SUBNET_IDS \
    --security-groups $ALB_SECURITY_GROUP \
    --region $AWS_REGION

# 8. 设置Auto Scaling
echo "📈 配置自动扩缩容..."
aws application-autoscaling register-scalable-target \
    --service-namespace ecs \
    --scalable-dimension ecs:service:DesiredCount \
    --resource-id service/$CLUSTER_NAME/$SERVICE_NAME \
    --min-capacity 2 \
    --max-capacity 10 \
    --region $AWS_REGION

aws application-autoscaling put-scaling-policy \
    --policy-name video-parser-cpu-scaling \
    --service-namespace ecs \
    --scalable-dimension ecs:service:DesiredCount \
    --resource-id service/$CLUSTER_NAME/$SERVICE_NAME \
    --policy-type TargetTrackingScaling \
    --target-tracking-scaling-policy-configuration '{
        "TargetValue": 70.0,
        "PredefinedMetricSpecification": {
            "PredefinedMetricType": "ECSServiceAverageCPUUtilization"
        },
        "ScaleOutCooldown": 300,
        "ScaleInCooldown": 300
    }' \
    --region $AWS_REGION

# 9. 设置CloudWatch监控
echo "📊 配置CloudWatch监控..."
aws logs create-log-group --log-group-name /ecs/video-parser --region $AWS_REGION

# 10. 创建CloudWatch告警
aws cloudwatch put-metric-alarm \
    --alarm-name "video-parser-high-cpu" \
    --alarm-description "Video Parser High CPU Usage" \
    --metric-name CPUUtilization \
    --namespace AWS/ECS \
    --statistic Average \
    --period 300 \
    --threshold 80 \
    --comparison-operator GreaterThanThreshold \
    --evaluation-periods 2 \
    --alarm-actions $SNS_TOPIC_ARN \
    --dimensions Name=ServiceName,Value=$SERVICE_NAME Name=ClusterName,Value=$CLUSTER_NAME \
    --region $AWS_REGION

echo "✅ AWS部署完成！"
echo "🌐 应用将在几分钟内可用"
echo "📊 监控面板: https://console.aws.amazon.com/ecs/home?region=$AWS_REGION#/clusters/$CLUSTER_NAME/services"

# 清理临时文件
rm -f task-definition.json

echo "🎉 部署成功完成！"
