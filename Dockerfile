# 短视频去水印统一解析接口 - Docker镜像
FROM php:8.1-fpm-alpine

# 设置工作目录
WORKDIR /var/www/html

# 安装系统依赖
RUN apk add --no-cache \
    nginx \
    supervisor \
    curl \
    wget \
    zip \
    unzip \
    git \
    redis \
    memcached \
    && rm -rf /var/cache/apk/*

# 安装PHP扩展
RUN docker-php-ext-install \
    pdo \
    pdo_mysql \
    mysqli \
    curl \
    json \
    mbstring \
    opcache

# 安装Redis和Memcached扩展
RUN pecl install redis memcached \
    && docker-php-ext-enable redis memcached

# 复制应用代码
COPY . /var/www/html/

# 设置权限
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html \
    && chmod -R 777 /var/www/html/cache \
    && chmod -R 777 /var/www/html/logs

# 复制配置文件
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/php.ini /usr/local/etc/php/php.ini
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 创建必要目录
RUN mkdir -p /var/log/nginx \
    && mkdir -p /var/log/supervisor \
    && mkdir -p /run/nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/admin/status.php || exit 1

# 启动命令
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
