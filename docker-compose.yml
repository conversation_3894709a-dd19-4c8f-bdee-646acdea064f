version: '3.8'

services:
  # 主应用服务
  app:
    build: .
    container_name: video_parser_app
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./cache:/var/www/html/cache
      - ./logs:/var/www/html/logs
      - ./config:/var/www/html/config
    environment:
      - PHP_MEMORY_LIMIT=512M
      - PHP_MAX_EXECUTION_TIME=60
      - CACHE_ENABLE=true
      - DB_ENABLE=true
      - DB_HOST=mysql
      - DB_NAME=video_parser
      - DB_USER=root
      - DB_PASS=password123
      - REDIS_HOST=redis
      - MEMCACHED_HOST=memcached
    depends_on:
      - mysql
      - redis
      - memcached
    networks:
      - video_parser_network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: video_parser_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: password123
      MYSQL_DATABASE: video_parser
      MYSQL_USER: app_user
      MY<PERSON><PERSON>_PASSWORD: app_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql.cnf:/etc/mysql/conf.d/custom.cnf
    ports:
      - "3306:3306"
    networks:
      - video_parser_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: video_parser_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - video_parser_network

  # Memcached缓存
  memcached:
    image: memcached:1.6-alpine
    container_name: video_parser_memcached
    restart: unless-stopped
    command: memcached -m 128
    ports:
      - "11211:11211"
    networks:
      - video_parser_network

  # Nginx负载均衡器（可选）
  nginx:
    image: nginx:alpine
    container_name: video_parser_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx-lb.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - video_parser_network

  # 监控服务
  prometheus:
    image: prom/prometheus
    container_name: video_parser_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - video_parser_network

  # 可视化监控
  grafana:
    image: grafana/grafana
    container_name: video_parser_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - video_parser_network

volumes:
  mysql_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  video_parser_network:
    driver: bridge
