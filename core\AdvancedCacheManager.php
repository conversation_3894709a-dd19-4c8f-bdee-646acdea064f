<?php
/**
 * 高级缓存管理器
 * 支持多层缓存、缓存预热、智能失效等功能
 */

class AdvancedCacheManager extends CacheManager
{
    private $memcached;
    private $redis;
    private $cacheStrategy;
    private $hitRatio = [];

    public function __construct($strategy = 'file')
    {
        parent::__construct();
        $this->cacheStrategy = $strategy;
        $this->initAdvancedCache();
    }

    /**
     * 初始化高级缓存
     */
    private function initAdvancedCache()
    {
        switch ($this->cacheStrategy) {
            case 'redis':
                $this->initRedis();
                break;
            case 'memcached':
                $this->initMemcached();
                break;
            case 'hybrid':
                $this->initRedis();
                $this->initMemcached();
                break;
        }
    }

    /**
     * 初始化Redis
     */
    private function initRedis()
    {
        if (class_exists('Redis')) {
            try {
                $this->redis = new Redis();
                $this->redis->connect('127.0.0.1', 6379);
                $this->redis->select(0); // 使用数据库0
            } catch (Exception $e) {
                $this->redis = null;
            }
        }
    }

    /**
     * 初始化Memcached
     */
    private function initMemcached()
    {
        if (class_exists('Memcached')) {
            try {
                $this->memcached = new Memcached();
                $this->memcached->addServer('127.0.0.1', 11211);
            } catch (Exception $e) {
                $this->memcached = null;
            }
        }
    }

    /**
     * 多层缓存获取
     */
    public function get($key)
    {
        $cacheKey = $this->generateCacheKey($key);
        
        // L1: 内存缓存 (Redis/Memcached)
        $data = $this->getFromMemoryCache($cacheKey);
        if ($data !== null) {
            $this->recordHit('memory');
            return $data;
        }

        // L2: 文件缓存
        $data = parent::get($key);
        if ($data !== null) {
            // 回写到内存缓存
            $this->setToMemoryCache($cacheKey, $data, CACHE_EXPIRE_TIME / 2);
            $this->recordHit('file');
            return $data;
        }

        $this->recordMiss();
        return null;
    }

    /**
     * 多层缓存设置
     */
    public function set($key, $data, $expireTime = null)
    {
        $cacheKey = $this->generateCacheKey($key);
        $expireTime = $expireTime ?: CACHE_EXPIRE_TIME;

        // 同时写入内存缓存和文件缓存
        $this->setToMemoryCache($cacheKey, $data, $expireTime);
        return parent::set($key, $data, $expireTime);
    }

    /**
     * 从内存缓存获取
     */
    private function getFromMemoryCache($key)
    {
        if ($this->redis) {
            try {
                $data = $this->redis->get($key);
                return $data !== false ? json_decode($data, true) : null;
            } catch (Exception $e) {
                // Redis失败，尝试Memcached
            }
        }

        if ($this->memcached) {
            try {
                return $this->memcached->get($key);
            } catch (Exception $e) {
                // Memcached也失败
            }
        }

        return null;
    }

    /**
     * 设置到内存缓存
     */
    private function setToMemoryCache($key, $data, $expireTime)
    {
        if ($this->redis) {
            try {
                $this->redis->setex($key, $expireTime, json_encode($data));
                return true;
            } catch (Exception $e) {
                // Redis失败，尝试Memcached
            }
        }

        if ($this->memcached) {
            try {
                return $this->memcached->set($key, $data, $expireTime);
            } catch (Exception $e) {
                // Memcached也失败
            }
        }

        return false;
    }

    /**
     * 生成缓存键
     */
    private function generateCacheKey($key)
    {
        return 'video_parser:' . md5($key);
    }

    /**
     * 缓存预热
     */
    public function warmup($urls)
    {
        foreach ($urls as $url) {
            $key = md5($url);
            if (!$this->get($key)) {
                // 异步预热（如果支持）
                if (function_exists('fastcgi_finish_request')) {
                    fastcgi_finish_request();
                }
                
                try {
                    // 这里需要调用解析逻辑进行预热
                    // $result = $this->parseUrl($url);
                    // $this->set($key, $result);
                } catch (Exception $e) {
                    // 预热失败，跳过
                }
            }
        }
    }

    /**
     * 智能缓存失效
     */
    public function invalidateByPattern($pattern)
    {
        if ($this->redis) {
            try {
                $keys = $this->redis->keys($pattern);
                if (!empty($keys)) {
                    $this->redis->del($keys);
                }
            } catch (Exception $e) {
                // Redis失败
            }
        }

        // 文件缓存失效
        $files = glob(CACHE_DIR . '*.cache');
        foreach ($files as $file) {
            if (preg_match($pattern, basename($file))) {
                unlink($file);
            }
        }
    }

    /**
     * 记录缓存命中
     */
    private function recordHit($type)
    {
        if (!isset($this->hitRatio[$type])) {
            $this->hitRatio[$type] = ['hits' => 0, 'total' => 0];
        }
        $this->hitRatio[$type]['hits']++;
        $this->hitRatio[$type]['total']++;
    }

    /**
     * 记录缓存未命中
     */
    private function recordMiss()
    {
        foreach ($this->hitRatio as &$ratio) {
            $ratio['total']++;
        }
    }

    /**
     * 获取缓存统计
     */
    public function getCacheStats()
    {
        $stats = parent::getStats();
        
        $stats['hit_ratio'] = [];
        foreach ($this->hitRatio as $type => $data) {
            $stats['hit_ratio'][$type] = $data['total'] > 0 ? 
                round(($data['hits'] / $data['total']) * 100, 2) : 0;
        }

        // Redis统计
        if ($this->redis) {
            try {
                $info = $this->redis->info();
                $stats['redis'] = [
                    'connected' => true,
                    'memory_usage' => $info['used_memory_human'] ?? 'N/A',
                    'keys' => $this->redis->dbSize()
                ];
            } catch (Exception $e) {
                $stats['redis'] = ['connected' => false];
            }
        }

        // Memcached统计
        if ($this->memcached) {
            try {
                $stats['memcached'] = [
                    'connected' => true,
                    'stats' => $this->memcached->getStats()
                ];
            } catch (Exception $e) {
                $stats['memcached'] = ['connected' => false];
            }
        }

        return $stats;
    }

    /**
     * 缓存压缩
     */
    public function compressCache()
    {
        $files = glob(CACHE_DIR . '*.cache');
        $compressed = 0;
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            $compressed_content = gzcompress($content, 6);
            
            if (strlen($compressed_content) < strlen($content)) {
                file_put_contents($file . '.gz', $compressed_content);
                unlink($file);
                $compressed++;
            }
        }
        
        return $compressed;
    }

    /**
     * 缓存分片
     */
    public function getShardedKey($key)
    {
        $hash = crc32($key);
        $shard = $hash % 16; // 16个分片
        return sprintf('shard_%02d_%s', $shard, $key);
    }
}
?>
