<?php
/**
 * 哔哩哔哩解析器
 */

class BilibiliParser extends AbstractParser
{
    private $cookie = ''; // B站Cookie，可以获取更高清晰度

    /**
     * 解析B站视频
     */
    public function parse($url)
    {
        try {
            $this->info("开始解析B站URL: $url");
            
            // 清理URL并提取BVID
            $bvid = $this->extractVideoId($url);
            if (!$bvid) {
                return $this->handleError('无法提取视频BVID');
            }

            $this->debug("提取到BVID: $bvid");

            // 获取视频信息
            $videoInfo = $this->getVideoInfo($bvid);
            if (!$videoInfo) {
                return $this->handleError('获取视频信息失败');
            }

            // 格式化数据
            $formattedData = $this->formatData($videoInfo);
            $formattedData['source_url'] = $url;

            $this->info("B站视频解析成功");
            return $formattedData;

        } catch (Exception $e) {
            return $this->handleError('解析过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * 提取视频BVID
     */
    protected function extractVideoId($url)
    {
        // 清理URL
        $cleanUrl = $this->httpClient->cleanUrl($url);
        $parsed = parse_url($cleanUrl);
        
        if (!$parsed) {
            return null;
        }

        $host = $parsed['host'] ?? '';
        $path = $parsed['path'] ?? '';

        // 处理短链接
        if ($host === 'b23.tv') {
            try {
                $headers = $this->httpClient->getHeaders($url);
                $redirectUrl = is_array($headers['Location']) ? end($headers['Location']) : $headers['Location'];
                $parsed = parse_url($redirectUrl);
                $path = $parsed['path'] ?? '';
            } catch (Exception $e) {
                return null;
            }
        }

        // 检查是否为视频链接
        if (strpos($path, '/video/') === false) {
            return null;
        }

        // 提取BVID
        $bvid = str_replace('/video/', '', $path);
        $bvid = rtrim($bvid, '/');
        
        return $bvid;
    }

    /**
     * 获取视频信息
     */
    protected function getVideoInfo($bvid)
    {
        try {
            $headers = [
                'Content-type: application/json;charset=UTF-8'
            ];

            $userAgent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36';

            // 获取视频基本信息
            $infoUrl = "https://api.bilibili.com/x/web-interface/view?bvid=$bvid";
            $infoResponse = $this->requestWithCookie($infoUrl, $headers, $userAgent);
            
            if (!$infoResponse) {
                return null;
            }

            $infoData = json_decode($infoResponse, true);
            if ($infoData['code'] !== 0) {
                return null;
            }

            $videoData = $infoData['data'];
            $firstPage = $videoData['pages'][0] ?? null;
            
            if (!$firstPage) {
                return null;
            }

            // 获取视频播放地址
            $playUrl = "https://api.bilibili.com/x/player/playurl?otype=json&fnver=0&fnval=3&player=3&qn=112&bvid=$bvid&cid={$firstPage['cid']}&platform=html5&high_quality=1";
            $playResponse = $this->requestWithCookie($playUrl, $headers, $userAgent);
            
            if (!$playResponse) {
                return null;
            }

            $playData = json_decode($playResponse, true);
            $videoUrl = '';
            
            if (isset($playData['data']['durl'][0]['url'])) {
                $originalUrl = $playData['data']['durl'][0]['url'];
                // 替换为可直接访问的URL
                $videoUrl = 'https://upos-sz-mirrorhw.bilivideo.com/' . explode('.bilivideo.com/', $originalUrl)[1];
            }

            // 构造标准化数据
            $data = [
                'title' => $videoData['title'] ?? '',
                'desc' => $videoData['desc'] ?? '',
                'author' => $videoData['owner']['name'] ?? '',
                'author_id' => $videoData['owner']['mid'] ?? '',
                'avatar' => $videoData['owner']['face'] ?? '',
                'cover' => $videoData['pic'] ?? '',
                'url' => $videoUrl,
                'duration' => $firstPage['duration'] ?? 0,
                'view' => $videoData['stat']['view'] ?? 0,
                'like' => $videoData['stat']['like'] ?? 0,
                'comment' => $videoData['stat']['reply'] ?? 0,
                'share' => $videoData['stat']['share'] ?? 0,
                'time' => $videoData['pubdate'] ?? 0
            ];

            return $data;

        } catch (Exception $e) {
            $this->debug("获取B站视频信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 带Cookie的请求
     */
    private function requestWithCookie($url, $headers, $userAgent)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_USERAGENT => $userAgent,
            CURLOPT_COOKIE => $this->cookie,
            CURLOPT_BINARYTRANSFER => true,
            CURLOPT_TIMEOUT => REQUEST_TIMEOUT
        ]);

        $output = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);

        if ($error) {
            throw new Exception("cURL error: $error");
        }

        return $output;
    }
}
?>
