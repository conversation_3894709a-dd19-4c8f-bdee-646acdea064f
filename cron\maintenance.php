#!/usr/bin/env php
<?php
/**
 * 系统维护定时任务
 * 用于清理缓存、日志、检查告警等
 */

// 设置执行环境
set_time_limit(0);
ini_set('memory_limit', '256M');

// 引入核心文件
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../core/CacheManager.php';
require_once __DIR__ . '/../core/DatabaseManager.php';
require_once __DIR__ . '/../core/AlertManager.php';
require_once __DIR__ . '/../core/SecurityManager.php';
require_once __DIR__ . '/../utils/Logger.php';

class MaintenanceTask
{
    private $logger;
    private $cacheManager;
    private $db;
    private $alertManager;
    private $securityManager;
    private $startTime;

    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->logger = new Logger();
        $this->cacheManager = new CacheManager();
        
        // 初始化数据库（如果启用）
        if (defined('DB_ENABLE') && DB_ENABLE) {
            try {
                $this->db = new DatabaseManager();
                $this->alertManager = new AlertManager($this->db);
            } catch (Exception $e) {
                $this->logger->warn("数据库初始化失败: " . $e->getMessage());
            }
        }
        
        $this->securityManager = new SecurityManager();
        
        $this->logger->info("维护任务开始执行");
    }

    /**
     * 执行所有维护任务
     */
    public function runAll()
    {
        $tasks = [
            'cleanExpiredCache' => '清理过期缓存',
            'cleanOldLogs' => '清理旧日志',
            'cleanOldData' => '清理过期数据',
            'checkAlerts' => '检查告警',
            'cleanSecurityBlocks' => '清理过期安全封禁',
            'optimizeDatabase' => '优化数据库',
            'generateReports' => '生成报告',
            'healthCheck' => '健康检查'
        ];

        $results = [];
        
        foreach ($tasks as $method => $description) {
            try {
                $this->logger->info("开始执行: $description");
                $startTime = microtime(true);
                
                $result = $this->$method();
                
                $duration = round((microtime(true) - $startTime) * 1000, 2);
                $results[$method] = [
                    'description' => $description,
                    'result' => $result,
                    'duration' => $duration,
                    'status' => 'success'
                ];
                
                $this->logger->info("完成: $description (耗时: {$duration}ms)");
                
            } catch (Exception $e) {
                $duration = round((microtime(true) - $startTime) * 1000, 2);
                $results[$method] = [
                    'description' => $description,
                    'error' => $e->getMessage(),
                    'duration' => $duration,
                    'status' => 'error'
                ];
                
                $this->logger->error("任务失败: $description - " . $e->getMessage());
            }
        }

        $this->logSummary($results);
        return $results;
    }

    /**
     * 清理过期缓存
     */
    private function cleanExpiredCache()
    {
        $cleaned = $this->cacheManager->cleanExpired();
        return "清理了 $cleaned 个过期缓存文件";
    }

    /**
     * 清理旧日志
     */
    private function cleanOldLogs()
    {
        $logDir = LOG_DIR;
        $cutoffTime = time() - (30 * 24 * 60 * 60); // 30天前
        $cleaned = 0;

        if (is_dir($logDir)) {
            $files = glob($logDir . '*.log');
            foreach ($files as $file) {
                if (filemtime($file) < $cutoffTime) {
                    unlink($file);
                    $cleaned++;
                }
            }
        }

        return "清理了 $cleaned 个过期日志文件";
    }

    /**
     * 清理过期数据
     */
    private function cleanOldData()
    {
        if (!$this->db) {
            return "数据库未启用，跳过";
        }

        $cleaned = $this->db->cleanupOldData(30); // 清理30天前的数据
        return "清理了 $cleaned 条过期数据记录";
    }

    /**
     * 检查告警
     */
    private function checkAlerts()
    {
        if (!$this->alertManager) {
            return "告警管理器未启用，跳过";
        }

        $this->alertManager->checkAlerts();
        return "告警检查完成";
    }

    /**
     * 清理过期安全封禁
     */
    private function cleanSecurityBlocks()
    {
        $cleaned = $this->securityManager->cleanupExpiredBlocks();
        return "清理了 $cleaned 个过期IP封禁";
    }

    /**
     * 优化数据库
     */
    private function optimizeDatabase()
    {
        if (!$this->db) {
            return "数据库未启用，跳过";
        }

        try {
            // 优化表
            $tables = ['api_statistics', 'access_logs', 'parse_records'];
            $optimized = 0;
            
            foreach ($tables as $table) {
                $sql = "OPTIMIZE TABLE $table";
                $this->db->pdo->exec($sql);
                $optimized++;
            }
            
            return "优化了 $optimized 个数据表";
            
        } catch (Exception $e) {
            return "数据库优化失败: " . $e->getMessage();
        }
    }

    /**
     * 生成报告
     */
    private function generateReports()
    {
        if (!$this->db) {
            return "数据库未启用，跳过报告生成";
        }

        try {
            // 生成日报
            $report = $this->generateDailyReport();
            
            // 保存报告
            $reportFile = LOG_DIR . 'daily_report_' . date('Y-m-d') . '.json';
            file_put_contents($reportFile, json_encode($report, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            return "生成了日报: " . basename($reportFile);
            
        } catch (Exception $e) {
            return "报告生成失败: " . $e->getMessage();
        }
    }

    /**
     * 生成日报
     */
    private function generateDailyReport()
    {
        $today = date('Y-m-d');
        
        // 获取统计数据
        $stats = $this->db->getStatistics(1); // 今天的数据
        
        // 计算总计
        $totalRequests = array_sum(array_column($stats, 'total_requests'));
        $totalSuccess = array_sum(array_column($stats, 'success_requests'));
        $totalFailed = array_sum(array_column($stats, 'failed_requests'));
        $avgResponseTime = count($stats) > 0 ? array_sum(array_column($stats, 'avg_response_time')) / count($stats) : 0;
        
        return [
            'date' => $today,
            'summary' => [
                'total_requests' => $totalRequests,
                'success_requests' => $totalSuccess,
                'failed_requests' => $totalFailed,
                'success_rate' => $totalRequests > 0 ? round(($totalSuccess / $totalRequests) * 100, 2) : 0,
                'avg_response_time' => round($avgResponseTime, 2)
            ],
            'platform_stats' => $stats,
            'popular_content' => $this->db->getPopularContent(10),
            'system_info' => [
                'php_version' => PHP_VERSION,
                'memory_peak' => memory_get_peak_usage(true),
                'disk_usage' => disk_total_space('.') - disk_free_space('.')
            ],
            'generated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 健康检查
     */
    private function healthCheck()
    {
        $checks = [];
        
        // 检查磁盘空间
        $total = disk_total_space('.');
        $free = disk_free_space('.');
        $usagePercent = (($total - $free) / $total) * 100;
        $checks['disk_space'] = [
            'status' => $usagePercent < 90 ? 'ok' : 'warning',
            'usage_percent' => round($usagePercent, 2)
        ];
        
        // 检查内存使用
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $memoryPercent = $memoryLimit > 0 ? ($memoryUsage / $memoryLimit) * 100 : 0;
        $checks['memory'] = [
            'status' => $memoryPercent < 80 ? 'ok' : 'warning',
            'usage_percent' => round($memoryPercent, 2)
        ];
        
        // 检查缓存目录
        $checks['cache_dir'] = [
            'status' => is_writable(CACHE_DIR) ? 'ok' : 'error',
            'writable' => is_writable(CACHE_DIR)
        ];
        
        // 检查日志目录
        $checks['log_dir'] = [
            'status' => is_writable(LOG_DIR) ? 'ok' : 'error',
            'writable' => is_writable(LOG_DIR)
        ];
        
        // 检查数据库连接
        if ($this->db) {
            try {
                $this->db->pdo->query('SELECT 1');
                $checks['database'] = ['status' => 'ok'];
            } catch (Exception $e) {
                $checks['database'] = ['status' => 'error', 'error' => $e->getMessage()];
            }
        }
        
        $healthyCount = count(array_filter($checks, function($check) {
            return $check['status'] === 'ok';
        }));
        
        return "健康检查完成: {$healthyCount}/" . count($checks) . " 项正常";
    }

    /**
     * 记录执行摘要
     */
    private function logSummary($results)
    {
        $totalDuration = round((microtime(true) - $this->startTime) * 1000, 2);
        $successCount = count(array_filter($results, function($r) { return $r['status'] === 'success'; }));
        $totalCount = count($results);
        
        $summary = [
            'total_tasks' => $totalCount,
            'successful_tasks' => $successCount,
            'failed_tasks' => $totalCount - $successCount,
            'total_duration' => $totalDuration,
            'memory_peak' => memory_get_peak_usage(true),
            'results' => $results
        ];
        
        $this->logger->info("维护任务执行完成", $summary);
        
        echo "维护任务执行摘要:\n";
        echo "- 总任务数: $totalCount\n";
        echo "- 成功: $successCount\n";
        echo "- 失败: " . ($totalCount - $successCount) . "\n";
        echo "- 总耗时: {$totalDuration}ms\n";
        echo "- 内存峰值: " . $this->formatBytes(memory_get_peak_usage(true)) . "\n";
        
        foreach ($results as $task => $result) {
            $status = $result['status'] === 'success' ? '✓' : '✗';
            $info = $result['status'] === 'success' ? $result['result'] : $result['error'];
            echo "$status {$result['description']}: $info ({$result['duration']}ms)\n";
        }
    }

    /**
     * 格式化字节大小
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit($limit)
    {
        if ($limit === '-1') return 0;
        
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;
        
        switch ($last) {
            case 'g': $limit *= 1024;
            case 'm': $limit *= 1024;
            case 'k': $limit *= 1024;
        }
        
        return $limit;
    }
}

// 执行维护任务
if (php_sapi_name() === 'cli') {
    $maintenance = new MaintenanceTask();
    $maintenance->runAll();
} else {
    echo "此脚本只能在命令行模式下运行\n";
    exit(1);
}
?>
