<?php
/**
 * 安全管理器
 * 提供WAF、DDoS防护、输入验证等安全功能
 */

class SecurityManager
{
    private $logger;
    private $blockedIPs = [];
    private $suspiciousPatterns = [];
    private $rateLimiter;

    public function __construct()
    {
        $this->logger = new Logger();
        $this->rateLimiter = new RateLimiter();
        $this->initSecurityRules();
    }

    /**
     * 初始化安全规则
     */
    private function initSecurityRules()
    {
        // 恶意模式检测
        $this->suspiciousPatterns = [
            // SQL注入
            '/(\bUNION\b|\bSELECT\b|\bINSERT\b|\bDELETE\b|\bDROP\b)/i',
            // XSS
            '/<script[^>]*>.*?<\/script>/i',
            '/javascript:/i',
            '/on\w+\s*=/i',
            // 路径遍历
            '/\.\.[\/\\\\]/i',
            // 命令注入
            '/[;&|`$()]/i',
        ];

        // 加载IP黑名单
        $this->loadBlockedIPs();
    }

    /**
     * 加载被封IP列表
     */
    private function loadBlockedIPs()
    {
        $blockFile = CACHE_DIR . 'blocked_ips.json';
        if (file_exists($blockFile)) {
            $data = json_decode(file_get_contents($blockFile), true);
            $this->blockedIPs = $data ?: [];
        }
    }

    /**
     * 保存被封IP列表
     */
    private function saveBlockedIPs()
    {
        $blockFile = CACHE_DIR . 'blocked_ips.json';
        file_put_contents($blockFile, json_encode($this->blockedIPs), LOCK_EX);
    }

    /**
     * 安全检查主入口
     */
    public function checkSecurity()
    {
        $clientIP = $this->getClientIP();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';

        // 1. IP黑名单检查
        if ($this->isIPBlocked($clientIP)) {
            $this->logger->warn("Blocked IP attempted access: $clientIP");
            $this->sendSecurityResponse(403, 'IP被封禁');
        }

        // 2. User-Agent检查
        if ($this->isSuspiciousUserAgent($userAgent)) {
            $this->logger->warn("Suspicious User-Agent: $userAgent from $clientIP");
            $this->blockIP($clientIP, 'Suspicious User-Agent');
        }

        // 3. 请求频率检查
        if (!$this->rateLimiter->isAllowed($clientIP)) {
            $this->logger->warn("Rate limit exceeded: $clientIP");
            $this->sendSecurityResponse(429, '请求过于频繁');
        }

        // 4. 恶意请求检查
        if ($this->isMaliciousRequest($requestUri)) {
            $this->logger->warn("Malicious request detected: $requestUri from $clientIP");
            $this->blockIP($clientIP, 'Malicious request');
        }

        // 5. 地理位置检查（可选）
        if (defined('GEO_BLOCKING') && GEO_BLOCKING) {
            if ($this->isGeoBlocked($clientIP)) {
                $this->sendSecurityResponse(403, '地理位置被限制');
            }
        }

        return true;
    }

    /**
     * 获取客户端真实IP
     */
    private function getClientIP()
    {
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // 代理
            'HTTP_X_REAL_IP',            // Nginx
            'HTTP_CLIENT_IP',            // 代理
            'REMOTE_ADDR'                // 直连
        ];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                // 处理多个IP的情况
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    /**
     * 检查IP是否被封
     */
    private function isIPBlocked($ip)
    {
        if (isset($this->blockedIPs[$ip])) {
            $blockInfo = $this->blockedIPs[$ip];
            // 检查封禁是否过期
            if (isset($blockInfo['expires']) && time() > $blockInfo['expires']) {
                unset($this->blockedIPs[$ip]);
                $this->saveBlockedIPs();
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 封禁IP
     */
    private function blockIP($ip, $reason, $duration = 3600)
    {
        $this->blockedIPs[$ip] = [
            'reason' => $reason,
            'blocked_at' => time(),
            'expires' => time() + $duration
        ];
        $this->saveBlockedIPs();
        $this->logger->warn("IP blocked: $ip, reason: $reason");
        $this->sendSecurityResponse(403, 'IP已被封禁');
    }

    /**
     * 检查可疑User-Agent
     */
    private function isSuspiciousUserAgent($userAgent)
    {
        if (empty($userAgent)) {
            return true; // 空User-Agent可疑
        }

        $suspiciousAgents = [
            'sqlmap',
            'nikto',
            'nmap',
            'masscan',
            'python-requests',
            'curl',
            'wget',
            'bot',
            'crawler',
            'spider'
        ];

        $userAgent = strtolower($userAgent);
        foreach ($suspiciousAgents as $agent) {
            if (strpos($userAgent, $agent) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查恶意请求
     */
    private function isMaliciousRequest($uri)
    {
        foreach ($this->suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $uri)) {
                return true;
            }
        }

        // 检查请求参数
        $allParams = array_merge($_GET, $_POST);
        foreach ($allParams as $key => $value) {
            if (is_string($value)) {
                foreach ($this->suspiciousPatterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 地理位置封禁检查
     */
    private function isGeoBlocked($ip)
    {
        // 这里可以集成GeoIP库进行地理位置检查
        // 示例：禁止某些国家的访问
        $blockedCountries = ['CN', 'RU', 'KP']; // 示例
        
        // 需要安装GeoIP扩展或使用第三方API
        // $country = geoip_country_code_by_name($ip);
        // return in_array($country, $blockedCountries);
        
        return false; // 暂时不启用
    }

    /**
     * 输入验证和清理
     */
    public function sanitizeInput($input)
    {
        if (is_array($input)) {
            return array_map([$this, 'sanitizeInput'], $input);
        }

        if (!is_string($input)) {
            return $input;
        }

        // 移除危险字符
        $input = strip_tags($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        
        // 检查恶意模式
        foreach ($this->suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                $this->logger->warn("Malicious input detected: $input");
                throw new Exception('输入包含恶意内容');
            }
        }

        return $input;
    }

    /**
     * CSRF保护
     */
    public function generateCSRFToken()
    {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        return $token;
    }

    /**
     * 验证CSRF Token
     */
    public function validateCSRFToken($token)
    {
        if (!isset($_SESSION)) {
            session_start();
        }
        
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * 发送安全响应
     */
    private function sendSecurityResponse($code, $message)
    {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode([
            'code' => $code,
            'msg' => $message,
            'timestamp' => time()
        ]);
        exit();
    }

    /**
     * 获取安全统计
     */
    public function getSecurityStats()
    {
        return [
            'blocked_ips' => count($this->blockedIPs),
            'active_blocks' => count(array_filter($this->blockedIPs, function($block) {
                return !isset($block['expires']) || time() < $block['expires'];
            })),
            'security_rules' => count($this->suspiciousPatterns),
            'last_attack' => $this->getLastAttackTime()
        ];
    }

    /**
     * 获取最后攻击时间
     */
    private function getLastAttackTime()
    {
        if (empty($this->blockedIPs)) {
            return null;
        }
        
        $lastTime = 0;
        foreach ($this->blockedIPs as $block) {
            if ($block['blocked_at'] > $lastTime) {
                $lastTime = $block['blocked_at'];
            }
        }
        
        return $lastTime > 0 ? date('Y-m-d H:i:s', $lastTime) : null;
    }

    /**
     * 清理过期封禁
     */
    public function cleanupExpiredBlocks()
    {
        $cleaned = 0;
        foreach ($this->blockedIPs as $ip => $block) {
            if (isset($block['expires']) && time() > $block['expires']) {
                unset($this->blockedIPs[$ip]);
                $cleaned++;
            }
        }
        
        if ($cleaned > 0) {
            $this->saveBlockedIPs();
        }
        
        return $cleaned;
    }
}
?>
