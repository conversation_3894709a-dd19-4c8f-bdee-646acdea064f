<?php
/**
 * 快手解析器 - 支持视频和图集
 */

class KuaishouParser extends AbstractParser
{
    /**
     * 解析快手视频/图集
     */
    public function parse($url)
    {
        try {
            $this->info("开始解析快手URL: $url");

            // 获取重定向后的URL
            $finalUrl = $this->getRedirectUrl($url);
            $this->debug("重定向后的URL: $finalUrl");

            // 提取视频ID和类型
            $urlInfo = $this->extractVideoId($finalUrl);
            if (!$urlInfo) {
                $this->debug("无法提取视频ID，尝试从原始URL提取");
                $urlInfo = $this->extractVideoId($url);
                if (!$urlInfo) {
                    $this->debug("原始URL也无法提取ID，解析失败", ['url' => $url, 'finalUrl' => $finalUrl]);
                    return $this->handleError('无法提取视频ID');
                }
            }

            $this->debug("提取到视频ID: {$urlInfo['id']}, 类型: {$urlInfo['type']}");

            // 根据类型选择解析方法
            if ($urlInfo['type'] === 'photo') {
                $this->debug("识别为图集，使用图集解析方法");
                $contentInfo = $this->parseImageContent($url, $urlInfo['id']);
            } elseif ($urlInfo['type'] === 'unknown') {
                $this->debug("识别为未知类型（短链接），使用智能解析方法");
                $contentInfo = $this->parseUnknownContent($finalUrl, $urlInfo['id']);
            } else {
                $this->debug("识别为视频，使用视频解析方法");
                $contentInfo = $this->parseVideoContent($urlInfo['id'], $finalUrl);
            }

            if (!$contentInfo) {
                $this->debug("内容解析失败，返回错误");
                return $this->handleError('获取内容信息失败');
            }

            $this->debug("内容解析成功，开始格式化数据", ['type' => $contentInfo['type'] ?? 'unknown']);

            // 格式化数据
            $formattedData = $this->formatData($contentInfo);
            $formattedData['source_url'] = $url;

            $this->info("快手内容解析成功");
            return $formattedData;

        } catch (Exception $e) {
            $this->debug("解析过程中发生异常", ['exception' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return $this->handleError('解析过程中发生错误: ' . $e->getMessage());
        }
    }

    /**
     * 获取重定向URL
     */
    private function getRedirectUrl($url)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_NOBODY => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT => 30
        ]);

        curl_exec($ch);
        $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        curl_close($ch);

        return $finalUrl ?: $url;
    }

    /**
     * 提取视频ID和类型
     */
    protected function extractVideoId($url)
    {
        $patterns = [
            '/short-video\/([^?]+)/' => 'video',    // 短视频格式
            '/photo\/([^?]+)/' => 'photo',          // 图片格式
            '/fw\/photo\/([^?]+)/' => 'photo',      // 另一种图片格式
            '/v\.kuaishou\.com\/([^?]+)/' => 'unknown', // 短链接格式，需要进一步判断
        ];

        foreach ($patterns as $pattern => $type) {
            if (preg_match($pattern, $url, $matches)) {
                return [
                    'id' => $matches[1],
                    'type' => $type
                ];
            }
        }

        return null;
    }

    /**
     * 解析视频内容
     */
    private function parseVideoContent($videoId, $finalUrl)
    {
        try {
            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ];

            // 构造请求URL
            $requestUrl = "https://www.kuaishou.com/short-video/$videoId";

            $response = $this->httpClient->get($requestUrl, $headers, 'desktop');
            if (!$response) {
                return null;
            }

            // 提取Apollo State数据
            $pattern = '/window\.__APOLLO_STATE__\s*=\s*(.*?)\<\/script>/s';
            if (!preg_match($pattern, $response, $matches)) {
                return null;
            }

            // 清理JavaScript函数
            $apolloState = $matches[1];
            $apolloState = preg_replace('/function\s*\([^)]*\)\s*{[^}]*}/', ':', $apolloState);
            $apolloState = preg_replace('/,\s*(?=}|])/', '', $apolloState);
            $apolloState = str_replace(';(:());', '', $apolloState);

            $jsonData = json_decode($apolloState, true);
            if (!$jsonData || !isset($jsonData['defaultClient'])) {
                return null;
            }

            $videoInfo = $jsonData['defaultClient'];
            $key = "VisionVideoDetailPhoto:$videoId";

            if (!isset($videoInfo[$key])) {
                return null;
            }

            $itemData = $videoInfo[$key];

            // 构造标准化数据
            return [
                'title' => $itemData['caption'] ?? '',
                'desc' => $itemData['caption'] ?? '',
                'author' => $itemData['userName'] ?? '',
                'author_id' => $itemData['userId'] ?? '',
                'avatar' => $itemData['headUrl'] ?? '',
                'cover' => $itemData['coverUrl'] ?? '',
                'url' => $itemData['photoUrl'] ?? '',
                'like' => $itemData['likeCount'] ?? 0,
                'comment' => $itemData['commentCount'] ?? 0,
                'view' => $itemData['viewCount'] ?? 0,
                'time' => $itemData['timestamp'] ?? 0,
                'duration' => $itemData['duration'] ?? 0,
                'width' => $itemData['width'] ?? 0,
                'height' => $itemData['height'] ?? 0,
                'type' => 'video'
            ];

        } catch (Exception $e) {
            $this->debug("获取快手视频信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 解析图集内容
     */
    private function parseImageContent($url, $videoId)
    {
        try {
            $this->debug("开始解析图集内容", ['url' => $url, 'videoId' => $videoId]);

            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ];

            // 获取重定向后的URL
            $this->debug("获取重定向URL");
            $redirectHeaders = get_headers($url, true, stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false
                ]
            ]));

            if (!$redirectHeaders) {
                $this->debug("获取重定向headers失败");
                return null;
            }

            $location = $redirectHeaders['Location'] ?? $url;
            $this->debug("重定向目标URL", ['location' => $location]);

            $this->debug("开始请求页面内容");
            $response = $this->httpClient->get($location, $headers, 'desktop');
            if (!$response) {
                $this->debug("HTTP请求失败");
                return null;
            }

            $this->debug("HTTP请求成功", ['responseLength' => strlen($response)]);

            // 提取INIT_STATE数据
            $this->debug("开始提取INIT_STATE数据");
            $pattern = '/window\.INIT_STATE\s*=\s*(.*?)\<\/script>/s';
            if (!preg_match($pattern, $response, $matches)) {
                $this->debug("未找到INIT_STATE数据");
                // 尝试其他可能的模式
                $alternativePatterns = [
                    '/window\.__INITIAL_STATE__\s*=\s*(.*?)\<\/script>/s',
                    '/window\.__APOLLO_STATE__\s*=\s*(.*?)\<\/script>/s'
                ];

                foreach ($alternativePatterns as $altPattern) {
                    if (preg_match($altPattern, $response, $matches)) {
                        $this->debug("找到替代数据源");
                        break;
                    }
                }

                if (!isset($matches[1])) {
                    $this->debug("所有数据源都未找到");
                    return null;
                }
            } else {
                $this->debug("成功找到INIT_STATE数据");
            }

            $jsonString = stripslashes($matches[1]);
            $this->debug("JSON字符串长度", ['length' => strlen($jsonString)]);

            $data = json_decode($jsonString, true);

            if (json_last_error() != JSON_ERROR_NONE) {
                $this->debug("JSON解析失败，使用原项目的清理方法", ['error' => json_last_error_msg()]);

                // 使用原项目的cleanInvalidJsonEscapes方法
                $cleanedJson = $this->cleanInvalidJsonEscapes($jsonString);
                $data = json_decode($cleanedJson, true);

                if (json_last_error() != JSON_ERROR_NONE) {
                    $this->debug("原项目清理方法也失败", ['error' => json_last_error_msg()]);
                    return null;
                } else {
                    $this->debug("原项目清理方法成功");
                }
            } else {
                $this->debug("JSON解析成功");
            }

            if (!is_array($data)) {
                $this->debug("解析结果不是数组");
                return null;
            }

            // 新发现：快手使用凯撒密码编码键名，需要解码
            $this->simpleLog("尝试解码快手JSON键名");
            $decodedData = $this->decodeKuaishouJson($data);

            if ($decodedData && count($decodedData) > 0) {
                $this->simpleLog("JSON解码成功，找到 " . count($decodedData) . " 个解码键");
                $data = $decodedData;
            } else {
                $this->simpleLog("JSON解码失败，使用原始数据");
            }

            $this->debug("数据结构检查", ['dataKeys' => array_slice(array_keys($data), 0, 10)]);

            // 查找包含fid的数据
            $this->debug("开始查找包含fid的数据");
            $filteredData = [];
            $foundKeys = [];

            foreach ($data as $key => $value) {
                // 记录所有键名，帮助调试
                $foundKeys[] = $key;

                // 修正条件：查找包含tusjoh开头且有fid的数据，或者直接包含photo的数据
                if ((strpos($key, 'tusjoh') === 0 && isset($value['fid'])) ||
                    (is_array($value) && isset($value['photo']))) {
                    $this->debug("找到匹配的键: $key");
                    $filteredData[$key] = $value;
                }
            }

            if (empty($filteredData)) {
                $this->debug("未找到包含fid或photo的数据", ['foundKeys' => array_slice($foundKeys, 0, 10)]);
                return null;
            }

            $this->debug("找到数据", ['count' => count($filteredData), 'keys' => array_keys($filteredData)]);

            // 获取第一个数据
            $firstValue = reset($filteredData);
            $this->debug("获取到数据", ['key' => key($filteredData)]);
            $images = [];
            $music = '';

            // 处理图片数据
            $this->debug("开始处理图片数据");

            // 检查数据结构
            $photoData = $firstValue['photo'] ?? $firstValue;
            $this->debug("photo数据结构", ['keys' => array_keys($photoData)]);

            if (isset($photoData['ext_params']['atlas']['list'])) {
                $this->debug("找到atlas图片列表");
                $imgList = $photoData['ext_params']['atlas']['list'];
                foreach ($imgList as $img) {
                    $images[] = 'http://tx2.a.yximgs.com/' . $img;
                }
                $music = 'http://txmov2.a.kwimgs.com' . ($photoData['ext_params']['atlas']['music'] ?? '');
                $this->debug("atlas处理完成", ['imageCount' => count($images)]);
            } elseif (isset($photoData['coverUrls'][0]['url'])) {
                $this->debug("找到coverUrls图片");
                $images[] = $photoData['coverUrls'][0]['url'];
                $music = 'http://txmov2.a.kwimgs.com' . ($photoData['music']['audioUrls'][0]['url'] ?? '');
                $this->debug("coverUrls处理完成", ['imageCount' => count($images)]);
            } else {
                $this->debug("未找到图片数据，尝试其他路径", ['photoKeys' => array_keys($photoData)]);

                // 尝试其他可能的图片路径
                if (isset($photoData['atlas']['list'])) {
                    $this->debug("找到直接atlas路径");
                    $imgList = $photoData['atlas']['list'];
                    foreach ($imgList as $img) {
                        $images[] = 'http://tx2.a.yximgs.com/' . $img;
                    }
                }
            }

            if (empty($images)) {
                $this->debug("没有提取到图片，解析失败");
                return null;
            }

            $this->debug("图集解析成功", ['imageCount' => count($images), 'title' => $photoData['caption'] ?? '']);

            return [
                'title' => $photoData['caption'] ?? '',
                'desc' => $photoData['caption'] ?? '',
                'author' => $photoData['userName'] ?? '',
                'author_id' => $photoData['userId'] ?? '',
                'avatar' => $photoData['headUrl'] ?? '',
                'cover' => $images[0] ?? '',
                'images' => $images,
                'music' => $music ?? '',
                'like' => $photoData['likeCount'] ?? 0,
                'comment' => $photoData['commentCount'] ?? 0,
                'view' => $photoData['viewCount'] ?? 0,
                'time' => $photoData['timestamp'] ?? 0,
                'type' => 'image'
            ];

        } catch (Exception $e) {
            $this->debug("获取快手图集信息失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 解析未知类型内容（短链接）
     */
    private function parseUnknownContent($finalUrl, $shortId)
    {
        try {
            // 先尝试作为视频解析
            $videoInfo = $this->parseAsVideo($finalUrl, $shortId);
            if ($videoInfo && !empty($videoInfo['url'])) {
                return $videoInfo;
            }

            // 如果视频解析失败，尝试作为图集解析
            $imageInfo = $this->parseAsImage($finalUrl, $shortId);
            if ($imageInfo && !empty($imageInfo['images'])) {
                return $imageInfo;
            }

            return null;

        } catch (Exception $e) {
            $this->debug("解析未知内容失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 尝试作为视频解析
     */
    private function parseAsVideo($finalUrl, $shortId)
    {
        try {
            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ];

            $response = $this->httpClient->get($finalUrl, $headers, 'desktop');
            if (!$response) {
                return null;
            }

            // 提取Apollo State数据
            $pattern = '/window\.__APOLLO_STATE__\s*=\s*(.*?)\<\/script>/s';
            if (!preg_match($pattern, $response, $matches)) {
                return null;
            }

            // 清理JavaScript函数
            $apolloState = $matches[1];
            $apolloState = preg_replace('/function\s*\([^)]*\)\s*{[^}]*}/', ':', $apolloState);
            $apolloState = preg_replace('/,\s*(?=}|])/', '', $apolloState);
            $apolloState = str_replace(';(:());', '', $apolloState);

            $jsonData = json_decode($apolloState, true);
            if (!$jsonData || !isset($jsonData['defaultClient'])) {
                return null;
            }

            $videoInfo = $jsonData['defaultClient'];

            // 尝试不同的key格式
            $possibleKeys = [
                "VisionVideoDetailPhoto:$shortId",
                "VisionVideoDetailPhoto:" . strtolower($shortId),
                "VisionVideoDetailPhoto:" . strtoupper($shortId)
            ];

            foreach ($possibleKeys as $key) {
                if (isset($videoInfo[$key])) {
                    $itemData = $videoInfo[$key];

                    // 检查是否有视频URL
                    if (isset($itemData['photoUrl']) && !empty($itemData['photoUrl'])) {
                        return [
                            'title' => $itemData['caption'] ?? '',
                            'desc' => $itemData['caption'] ?? '',
                            'author' => $itemData['userName'] ?? '',
                            'author_id' => $itemData['userId'] ?? '',
                            'avatar' => $itemData['headUrl'] ?? '',
                            'cover' => $itemData['coverUrl'] ?? '',
                            'url' => $itemData['photoUrl'],
                            'like' => $itemData['likeCount'] ?? 0,
                            'comment' => $itemData['commentCount'] ?? 0,
                            'view' => $itemData['viewCount'] ?? 0,
                            'time' => $itemData['timestamp'] ?? 0,
                            'duration' => $itemData['duration'] ?? 0,
                            'width' => $itemData['width'] ?? 0,
                            'height' => $itemData['height'] ?? 0,
                            'type' => 'video'
                        ];
                    }
                }
            }

            return null;

        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 尝试作为图集解析
     */
    private function parseAsImage($finalUrl, $shortId)
    {
        try {
            $headers = [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            ];

            $response = $this->httpClient->get($finalUrl, $headers, 'desktop');
            if (!$response) {
                return null;
            }

            // 提取INIT_STATE数据
            $pattern = '/window\.INIT_STATE\s*=\s*(.*?)\<\/script>/s';
            if (!preg_match($pattern, $response, $matches)) {
                return null;
            }

            $jsonString = stripslashes($matches[1]);
            $data = json_decode($jsonString, true);

            if (json_last_error() != JSON_ERROR_NONE) {
                // 清理无效的JSON转义字符
                $jsonString = preg_replace('/\\\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})/', '', $jsonString);
                $data = json_decode($jsonString, true);
            }

            if (!is_array($data)) {
                return null;
            }

            // 查找包含fid的数据
            $filteredData = [];
            foreach ($data as $key => $value) {
                if (strpos($key, 'tusjoh') === 0 && isset($value['fid'])) {
                    $filteredData[$key] = $value;
                }
            }

            if (empty($filteredData)) {
                return null;
            }

            // 获取第一个数据
            $firstValue = reset($filteredData);
            $images = [];
            $music = '';

            // 处理图片数据
            if (isset($firstValue['photo']['ext_params']['atlas']['list'])) {
                $imgList = $firstValue['photo']['ext_params']['atlas']['list'];
                foreach ($imgList as $img) {
                    $images[] = 'http://tx2.a.yximgs.com/' . $img;
                }
                $music = 'http://txmov2.a.kwimgs.com' . ($firstValue['photo']['ext_params']['atlas']['music'] ?? '');
            } elseif (isset($firstValue['photo']['coverUrls'][0]['url'])) {
                $images[] = $firstValue['photo']['coverUrls'][0]['url'];
                $music = 'http://txmov2.a.kwimgs.com' . ($firstValue['photo']['music']['audioUrls'][0]['url'] ?? '');
            }

            if (!empty($images)) {
                return [
                    'title' => $firstValue['photo']['caption'] ?? '',
                    'desc' => $firstValue['photo']['caption'] ?? '',
                    'author' => $firstValue['photo']['userName'] ?? '',
                    'author_id' => $firstValue['photo']['userId'] ?? '',
                    'avatar' => $firstValue['photo']['headUrl'] ?? '',
                    'cover' => $images[0] ?? '',
                    'images' => $images,
                    'music' => $music,
                    'like' => $firstValue['photo']['likeCount'] ?? 0,
                    'comment' => $firstValue['photo']['commentCount'] ?? 0,
                    'view' => $firstValue['photo']['viewCount'] ?? 0,
                    'time' => $firstValue['photo']['timestamp'] ?? 0,
                    'type' => 'image'
                ];
            }

            return null;

        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 清理无效的JSON转义字符（来自原项目）
     */
    private function cleanInvalidJsonEscapes($jsonStr)
    {
        // 处理非法Unicode转义序列（如：\ufu3KP → \ufu3 KP → 删除非法部分）
        $jsonStr = preg_replace_callback(
            '/\\\\u([0-9a-fA-F]{0,4})([0-9a-fA-F]*)([^0-9a-fA-F].*?)(?=\\\\u|$)/',
            function($matches) {
                $validPart = '';
                $extraPart = '';

                // 如果前4位是合法十六进制，保留为有效的\uXXXX
                if (strlen($matches[1]) === 4) {
                    $validPart = '\\u' . $matches[1];
                    $extraPart = $matches[2] . $matches[3];
                }
                // 不足4位但后续有十六进制字符，补足4位
                elseif (strlen($matches[1]) + strlen($matches[2]) >= 4) {
                    $hexChars = $matches[1] . $matches[2];
                    $validPart = '\\u' . substr($hexChars, 0, 4);
                    $extraPart = substr($hexChars, 4) . $matches[3];
                }
                // 完全非法，删除\u
                else {
                    $extraPart = $matches[1] . $matches[2] . $matches[3];
                }

                // 保留非转义的字符部分
                return $validPart . (empty($extraPart) ? '' : ' ' . $extraPart);
            },
            $jsonStr
        );

        // 移除剩余的非法转义字符（保留合法的JSON转义）
        $jsonStr = preg_replace(
            '/\\\\([^"\\/bfnrtu])/',
            '$1',
            $jsonStr
        );

        // 修复单引号为双引号
        $jsonStr = str_replace("'", '"', $jsonStr);

        // 移除多余的分号
        $jsonStr = preg_replace('/;([^"]*")/', '$1', $jsonStr);

        return $jsonStr;
    }

    /**
     * 解码快手JSON（凯撒密码偏移1）
     */
    private function decodeKuaishouJson($data)
    {
        if (!is_array($data)) {
            return null;
        }

        $decodedData = [];

        foreach ($data as $key => $value) {
            // 解码键名
            $decodedKey = $this->caesarDecode($key, 1);

            // 递归解码值（如果值也是数组）
            if (is_array($value)) {
                $decodedValue = $this->decodeKuaishouJson($value);
            } else {
                $decodedValue = $value;
            }

            $decodedData[$decodedKey] = $decodedValue;
        }

        return $decodedData;
    }

    /**
     * 凯撒密码解码
     */
    private function caesarDecode($str, $shift)
    {
        $result = '';
        for ($i = 0; $i < strlen($str); $i++) {
            $char = $str[$i];
            if (ctype_alpha($char)) {
                $ascii = ord($char);
                $base = ctype_upper($char) ? ord('A') : ord('a');
                $shifted = (($ascii - $base - $shift + 26) % 26) + $base;
                $result .= chr($shifted);
            } else {
                $result .= $char;
            }
        }
        return $result;
    }

    /**
     * 简单日志记录
     */
    private function simpleLog($message)
    {
        $logFile = __DIR__ . '/../../logs/debug.log';
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] [kuaishou] $message\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }

    /**
     * 获取视频信息（兼容抽象类）
     */
    protected function getVideoInfo($videoId)
    {
        return $this->parseVideoContent($videoId, '');
    }
}
?>
