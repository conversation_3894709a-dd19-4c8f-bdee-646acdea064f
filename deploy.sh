#!/bin/bash

# 短视频去水印统一解析接口 - 一键部署脚本
# 支持多种部署方式：单机、<PERSON><PERSON>、Kubernetes、云服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查PHP版本
    if command -v php &> /dev/null; then
        PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
        if [[ $(echo "$PHP_VERSION >= 7.4" | bc -l) -eq 1 ]]; then
            log_success "PHP版本: $PHP_VERSION ✓"
        else
            log_error "PHP版本过低，需要7.4或更高版本"
            exit 1
        fi
    else
        log_error "未找到PHP，请先安装PHP"
        exit 1
    fi
    
    # 检查必需的PHP扩展
    REQUIRED_EXTENSIONS=("curl" "json" "mbstring")
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if php -m | grep -q "$ext"; then
            log_success "PHP扩展 $ext ✓"
        else
            log_error "缺少PHP扩展: $ext"
            exit 1
        fi
    done
}

# 单机部署
deploy_standalone() {
    log_info "开始单机部署..."
    
    # 设置权限
    chmod 755 -R .
    chmod 777 cache/ logs/ 2>/dev/null || mkdir -p cache logs && chmod 777 cache logs
    
    # 检查Web服务器
    if command -v nginx &> /dev/null; then
        log_info "检测到Nginx，配置虚拟主机..."
        setup_nginx
    elif command -v apache2 &> /dev/null || command -v httpd &> /dev/null; then
        log_info "检测到Apache，配置虚拟主机..."
        setup_apache
    else
        log_warning "未检测到Web服务器，使用PHP内置服务器"
        start_php_server
    fi
    
    log_success "单机部署完成！"
}

# 配置Nginx
setup_nginx() {
    cat > /tmp/video-parser.conf << 'EOF'
server {
    listen 80;
    server_name localhost;
    root /var/www/html/unified_api;
    index index.php;

    location / {
        try_files $uri $uri/ $uri.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ ^/(config|logs)/ {
        deny all;
    }
}
EOF
    
    log_info "Nginx配置已生成到 /tmp/video-parser.conf"
    log_info "请手动复制到Nginx配置目录并重启Nginx"
}

# 配置Apache
setup_apache() {
    cat > .htaccess << 'EOF'
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

<Files "config/*">
    Deny from all
</Files>

<Files "logs/*">
    Deny from all
</Files>
EOF
    
    log_success "Apache .htaccess配置已生成"
}

# 启动PHP内置服务器
start_php_server() {
    log_info "启动PHP内置服务器..."
    nohup php -S 0.0.0.0:8080 -t . > php_server.log 2>&1 &
    echo $! > php_server.pid
    log_success "PHP服务器已启动，访问地址: http://localhost:8080"
}

# Docker部署
deploy_docker() {
    log_info "开始Docker部署..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查docker-compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose未安装，请先安装docker-compose"
        exit 1
    fi
    
    # 构建并启动服务
    log_info "构建Docker镜像..."
    docker-compose build
    
    log_info "启动服务..."
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "Docker部署完成！"
        log_info "访问地址: http://localhost:8080"
        log_info "管理面板: http://localhost:8080/admin/status.php"
    else
        log_error "服务启动失败，请检查日志"
        docker-compose logs
        exit 1
    fi
}

# Kubernetes部署
deploy_kubernetes() {
    log_info "开始Kubernetes部署..."
    
    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl未安装，请先安装kubectl"
        exit 1
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi
    
    # 应用配置
    log_info "应用Kubernetes配置..."
    kubectl apply -f k8s/
    
    # 等待部署完成
    log_info "等待部署完成..."
    kubectl wait --for=condition=available --timeout=300s deployment/video-parser-app
    
    # 获取服务地址
    SERVICE_IP=$(kubectl get service video-parser-service -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [ -z "$SERVICE_IP" ]; then
        SERVICE_IP=$(kubectl get service video-parser-service -o jsonpath='{.spec.clusterIP}')
        log_info "集群内部访问地址: http://$SERVICE_IP"
    else
        log_success "外部访问地址: http://$SERVICE_IP"
    fi
    
    log_success "Kubernetes部署完成！"
}

# 云服务部署
deploy_cloud() {
    log_info "开始云服务部署..."
    
    echo "请选择云服务提供商:"
    echo "1) AWS"
    echo "2) 阿里云"
    echo "3) 腾讯云"
    read -p "请输入选择 (1-3): " cloud_choice
    
    case $cloud_choice in
        1)
            deploy_aws
            ;;
        2)
            deploy_aliyun
            ;;
        3)
            deploy_tencent
            ;;
        *)
            log_error "无效选择"
            exit 1
            ;;
    esac
}

# AWS部署
deploy_aws() {
    log_info "部署到AWS..."
    
    if [ ! -f "deploy/aws-deploy.sh" ]; then
        log_error "AWS部署脚本不存在"
        exit 1
    fi
    
    chmod +x deploy/aws-deploy.sh
    ./deploy/aws-deploy.sh
}

# 阿里云部署
deploy_aliyun() {
    log_info "部署到阿里云..."
    log_warning "阿里云部署脚本开发中..."
}

# 腾讯云部署
deploy_tencent() {
    log_info "部署到腾讯云..."
    log_warning "腾讯云部署脚本开发中..."
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    if [ -f "test/test.php" ]; then
        php test/test.php
    else
        log_warning "测试文件不存在"
    fi
}

# 显示帮助信息
show_help() {
    echo "短视频去水印统一解析接口 - 部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -t, --type TYPE     部署类型 (standalone|docker|kubernetes|cloud)"
    echo "  -h, --help          显示帮助信息"
    echo "  --test              运行测试"
    echo "  --check             检查系统要求"
    echo ""
    echo "部署类型:"
    echo "  standalone          单机部署（默认）"
    echo "  docker              Docker容器部署"
    echo "  kubernetes          Kubernetes集群部署"
    echo "  cloud               云服务部署"
    echo ""
    echo "示例:"
    echo "  $0                  # 交互式部署"
    echo "  $0 -t docker        # Docker部署"
    echo "  $0 --test           # 运行测试"
}

# 主函数
main() {
    echo "🎬 短视频去水印统一解析接口 - 部署脚本"
    echo "================================================"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -t|--type)
                DEPLOY_TYPE="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            --test)
                run_tests
                exit 0
                ;;
            --check)
                check_requirements
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查系统要求
    check_requirements
    
    # 如果没有指定部署类型，进入交互模式
    if [ -z "$DEPLOY_TYPE" ]; then
        echo ""
        echo "请选择部署方式:"
        echo "1) 单机部署 (推荐新手)"
        echo "2) Docker部署 (推荐)"
        echo "3) Kubernetes部署"
        echo "4) 云服务部署"
        echo ""
        read -p "请输入选择 (1-4): " choice
        
        case $choice in
            1) DEPLOY_TYPE="standalone" ;;
            2) DEPLOY_TYPE="docker" ;;
            3) DEPLOY_TYPE="kubernetes" ;;
            4) DEPLOY_TYPE="cloud" ;;
            *) log_error "无效选择"; exit 1 ;;
        esac
    fi
    
    # 执行部署
    case $DEPLOY_TYPE in
        standalone)
            deploy_standalone
            ;;
        docker)
            deploy_docker
            ;;
        kubernetes)
            deploy_kubernetes
            ;;
        cloud)
            deploy_cloud
            ;;
        *)
            log_error "不支持的部署类型: $DEPLOY_TYPE"
            exit 1
            ;;
    esac
    
    # 运行测试
    echo ""
    read -p "是否运行测试? (y/N): " run_test
    if [[ $run_test =~ ^[Yy]$ ]]; then
        run_tests
    fi
    
    log_success "🎉 部署完成！"
    echo ""
    echo "📚 文档: README.md"
    echo "🔧 配置: config/config.php"
    echo "📊 监控: /admin/status.php"
    echo "🧪 测试: /test/test.php"
}

# 执行主函数
main "$@"
