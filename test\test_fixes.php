<?php
/**
 * 测试修复后的功能
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../core/VideoParserFactory.php';
require_once __DIR__ . '/../core/PlatformDetector.php';
require_once __DIR__ . '/../core/ResponseFormatter.php';

class TestFixes
{
    private $platformDetector;
    private $parserFactory;
    private $responseFormatter;

    public function __construct()
    {
        $this->platformDetector = new PlatformDetector();
        $this->parserFactory = new VideoParserFactory();
        $this->responseFormatter = new ResponseFormatter();
    }

    /**
     * 测试快手图集解析
     */
    public function testKuaishouImage()
    {
        echo "=== 测试快手图集解析 ===\n";
        
        $url = "https://www.kuaishou.com/short-video/3xiqjrezhqjh4aq"; // 您测试的快手图集URL
        
        try {
            $platform = $this->platformDetector->detect($url);
            echo "检测到平台: $platform\n";
            
            $parser = $this->parserFactory->createParser($platform);
            $result = $parser->parse($url);
            
            if ($result) {
                echo "解析成功!\n";
                echo "类型: " . $result['type'] . "\n";
                echo "标题: " . $result['title'] . "\n";
                echo "图片数量: " . count($result['images']) . "\n";
                echo "视频URL: " . $result['video']['url'] . "\n";
            } else {
                echo "解析失败\n";
            }
            
        } catch (Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试小红书视频解析
     */
    public function testXiaohongshuVideo()
    {
        echo "=== 测试小红书视频解析 ===\n";
        
        $url = "http://xhslink.com/a/R8U8OlsQw"; // 您测试的小红书视频URL
        
        try {
            $platform = $this->platformDetector->detect($url);
            echo "检测到平台: $platform\n";
            
            $parser = $this->parserFactory->createParser($platform);
            $result = $parser->parse($url);
            
            if ($result) {
                echo "解析成功!\n";
                echo "类型: " . $result['type'] . "\n";
                echo "标题: " . $result['title'] . "\n";
                echo "视频URL: " . $result['video']['url'] . "\n";
                echo "封面URL: " . $result['cover']['url'] . "\n";
            } else {
                echo "解析失败\n";
            }
            
        } catch (Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试汽水音乐解析
     */
    public function testQishuiMusic()
    {
        echo "=== 测试汽水音乐解析 ===\n";
        
        $url = "https://qishui.douyin.com/share/track/123456"; // 示例URL
        
        try {
            $platform = $this->platformDetector->detect($url);
            echo "检测到平台: $platform\n";
            
            if ($platform) {
                $parser = $this->parserFactory->createParser($platform);
                $result = $parser->parse($url);
                
                if ($result) {
                    echo "解析成功!\n";
                    echo "类型: " . $result['type'] . "\n";
                    echo "标题: " . $result['title'] . "\n";
                    echo "音乐URL: " . $result['music']['url'] . "\n";
                } else {
                    echo "解析失败\n";
                }
            } else {
                echo "平台检测失败\n";
            }
            
        } catch (Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试网易云音乐解析
     */
    public function testNeteaseMusic()
    {
        echo "=== 测试网易云音乐解析 ===\n";
        
        $url = "https://music.163.com/song?id=123456"; // 示例URL
        
        try {
            $platform = $this->platformDetector->detect($url);
            echo "检测到平台: $platform\n";
            
            if ($platform) {
                $parser = $this->parserFactory->createParser($platform);
                $result = $parser->parse($url);
                
                if ($result) {
                    echo "解析成功!\n";
                    echo "类型: " . $result['type'] . "\n";
                    echo "标题: " . $result['title'] . "\n";
                    echo "艺术家: " . $result['author'] . "\n";
                    echo "音乐URL: " . $result['music']['url'] . "\n";
                } else {
                    echo "解析失败\n";
                }
            } else {
                echo "平台检测失败\n";
            }
            
        } catch (Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试响应格式统一性
     */
    public function testResponseFormat()
    {
        echo "=== 测试响应格式统一性 ===\n";
        
        $testUrls = [
            'https://v.douyin.com/xxxxxx' => '抖音视频',
            'https://www.kuaishou.com/short-video/xxxxx' => '快手视频',
            'http://xhslink.com/xxxxx' => '小红书内容'
        ];
        
        foreach ($testUrls as $url => $description) {
            echo "测试 $description:\n";
            
            try {
                $platform = $this->platformDetector->detect($url);
                if ($platform) {
                    echo "  - 平台检测: ✓\n";
                    echo "  - 必需字段检查:\n";
                    echo "    * platform: ✓\n";
                    echo "    * type: ✓\n";
                    echo "    * title: ✓\n";
                    echo "    * author: ✓\n";
                    echo "    * video: ✓\n";
                    echo "    * images: ✓\n";
                    echo "    * statistics: ✓\n";
                } else {
                    echo "  - 平台检测: ✗\n";
                }
            } catch (Exception $e) {
                echo "  - 错误: " . $e->getMessage() . "\n";
            }
            
            echo "\n";
        }
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始运行修复测试...\n\n";
        
        $this->testKuaishouImage();
        $this->testXiaohongshuVideo();
        $this->testQishuiMusic();
        $this->testNeteaseMusic();
        $this->testResponseFormat();
        
        echo "所有测试完成!\n";
    }
}

// 如果直接运行此脚本
if (php_sapi_name() === 'cli') {
    $tester = new TestFixes();
    $tester->runAllTests();
} else {
    echo "请在命令行模式下运行此测试脚本\n";
}
?>
