<?php
/**
 * 速率限制器
 * 防止接口被滥用
 */

class RateLimiter
{
    private $cacheDir;
    private $maxRequests;
    private $timeWindow;

    public function __construct($maxRequests = 100, $timeWindow = 3600)
    {
        $this->cacheDir = CACHE_DIR . 'rate_limit/';
        $this->maxRequests = $maxRequests;
        $this->timeWindow = $timeWindow;
        
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

    /**
     * 检查是否允许请求
     */
    public function isAllowed($identifier)
    {
        $filePath = $this->cacheDir . md5($identifier) . '.json';
        $currentTime = time();
        
        // 读取现有记录
        $data = [];
        if (file_exists($filePath)) {
            $content = file_get_contents($filePath);
            if ($content) {
                $data = json_decode($content, true) ?: [];
            }
        }

        // 清理过期记录
        $data = array_filter($data, function($timestamp) use ($currentTime) {
            return ($currentTime - $timestamp) < $this->timeWindow;
        });

        // 检查请求数量
        if (count($data) >= $this->maxRequests) {
            return false;
        }

        // 记录新请求
        $data[] = $currentTime;
        file_put_contents($filePath, json_encode($data), LOCK_EX);

        return true;
    }

    /**
     * 获取剩余请求次数
     */
    public function getRemainingRequests($identifier)
    {
        $filePath = $this->cacheDir . md5($identifier) . '.json';
        $currentTime = time();
        
        $data = [];
        if (file_exists($filePath)) {
            $content = file_get_contents($filePath);
            if ($content) {
                $data = json_decode($content, true) ?: [];
            }
        }

        // 清理过期记录
        $data = array_filter($data, function($timestamp) use ($currentTime) {
            return ($currentTime - $timestamp) < $this->timeWindow;
        });

        return max(0, $this->maxRequests - count($data));
    }

    /**
     * 重置限制
     */
    public function reset($identifier)
    {
        $filePath = $this->cacheDir . md5($identifier) . '.json';
        if (file_exists($filePath)) {
            unlink($filePath);
        }
    }

    /**
     * 清理过期文件
     */
    public function cleanup()
    {
        $files = glob($this->cacheDir . '*.json');
        $currentTime = time();
        $cleaned = 0;

        foreach ($files as $file) {
            $content = file_get_contents($file);
            if (!$content) {
                unlink($file);
                $cleaned++;
                continue;
            }

            $data = json_decode($content, true);
            if (!$data) {
                unlink($file);
                $cleaned++;
                continue;
            }

            // 清理过期记录
            $validData = array_filter($data, function($timestamp) use ($currentTime) {
                return ($currentTime - $timestamp) < $this->timeWindow;
            });

            if (empty($validData)) {
                unlink($file);
                $cleaned++;
            } else {
                file_put_contents($file, json_encode(array_values($validData)), LOCK_EX);
            }
        }

        return $cleaned;
    }
}
?>
