<?php
/**
 * 视频解析器工厂
 * 根据平台创建对应的解析器实例
 */

require_once __DIR__ . '/AbstractParser.php';

class VideoParserFactory
{
    private $supportedPlatforms;
    private $parsers = [];

    public function __construct()
    {
        $this->supportedPlatforms = getSupportedPlatforms();
    }

    /**
     * 创建解析器实例
     */
    public function createParser($platform)
    {
        if (!$this->isSupported($platform)) {
            return null;
        }

        // 如果已经创建过，直接返回
        if (isset($this->parsers[$platform])) {
            return $this->parsers[$platform];
        }

        $config = $this->supportedPlatforms[$platform];
        $className = $config['class'];
        $filePath = __DIR__ . "/parsers/{$className}.php";

        // 检查解析器文件是否存在
        if (!file_exists($filePath)) {
            return null;
        }

        require_once $filePath;

        // 检查类是否存在
        if (!class_exists($className)) {
            return null;
        }

        // 创建解析器实例
        $parser = new $className($platform);
        
        // 验证解析器是否继承自AbstractParser
        if (!($parser instanceof AbstractParser)) {
            return null;
        }

        // 缓存解析器实例
        $this->parsers[$platform] = $parser;

        return $parser;
    }

    /**
     * 检查平台是否支持
     */
    public function isSupported($platform)
    {
        return isset($this->supportedPlatforms[$platform]);
    }

    /**
     * 获取所有支持的平台
     */
    public function getSupportedPlatforms()
    {
        return array_keys($this->supportedPlatforms);
    }

    /**
     * 获取平台配置信息
     */
    public function getPlatformConfig($platform)
    {
        return $this->supportedPlatforms[$platform] ?? null;
    }
}
?>
